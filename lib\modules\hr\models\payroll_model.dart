// part 'payroll_model.g.dart'; // سيتم تفعيلها عند الحاجة

/// نموذج الرواتب والمكافآت متوافق مع Backend API
class EmployeePayroll {
  final int id;
  final int employeeId;
  final DateTime payPeriodStart;
  final DateTime payPeriodEnd;
  final double basicSalary;
  final double allowances;
  final double bonuses;
  final double incentives;
  final double deductions;
  final double advanceDeductions;
  final double financialAssistance;
  final double netSalary;
  final DateTime? paymentDate;
  final String paymentStatus; // pending, paid, cancelled
  final String? notes;
  final int createdBy;
  final int createdAt;
  final int? updatedAt;

  // Navigation Properties
  final String? employeeName;
  final String? employeeNameArabic;
  final String? creatorName;

  const EmployeePayroll({
    required this.id,
    required this.employeeId,
    required this.payPeriodStart,
    required this.payPeriodEnd,
    required this.basicSalary,
    this.allowances = 0,
    this.bonuses = 0,
    this.incentives = 0,
    this.deductions = 0,
    this.advanceDeductions = 0,
    this.financialAssistance = 0,
    required this.netSalary,
    this.paymentDate,
    this.paymentStatus = 'pending',
    this.notes,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.employeeName,
    this.employeeNameArabic,
    this.creatorName,
  });

  factory EmployeePayroll.fromJson(Map<String, dynamic> json) {
    return EmployeePayroll(
      id: json['id'] as int,
      employeeId: json['employeeId'] as int,
      payPeriodStart: DateTime.parse(json['payPeriodStart'] as String),
      payPeriodEnd: DateTime.parse(json['payPeriodEnd'] as String),
      basicSalary: (json['basicSalary'] as num).toDouble(),
      allowances: (json['allowances'] as num?)?.toDouble() ?? 0,
      bonuses: (json['bonuses'] as num?)?.toDouble() ?? 0,
      incentives: (json['incentives'] as num?)?.toDouble() ?? 0,
      deductions: (json['deductions'] as num?)?.toDouble() ?? 0,
      advanceDeductions: (json['advanceDeductions'] as num?)?.toDouble() ?? 0,
      financialAssistance: (json['financialAssistance'] as num?)?.toDouble() ?? 0,
      netSalary: (json['netSalary'] as num).toDouble(),
      paymentDate: json['paymentDate'] != null ? DateTime.parse(json['paymentDate'] as String) : null,
      paymentStatus: json['paymentStatus'] as String? ?? 'pending',
      notes: json['notes'] as String?,
      createdBy: json['createdBy'] as int,
      createdAt: json['createdAt'] as int,
      updatedAt: json['updatedAt'] as int?,
      employeeName: json['employeeName'] as String?,
      employeeNameArabic: json['employeeNameArabic'] as String?,
      creatorName: json['creatorName'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'payPeriodStart': payPeriodStart.toIso8601String(),
      'payPeriodEnd': payPeriodEnd.toIso8601String(),
      'basicSalary': basicSalary,
      'allowances': allowances,
      'bonuses': bonuses,
      'incentives': incentives,
      'deductions': deductions,
      'advanceDeductions': advanceDeductions,
      'financialAssistance': financialAssistance,
      'netSalary': netSalary,
      'paymentDate': paymentDate?.toIso8601String(),
      'paymentStatus': paymentStatus,
      'notes': notes,
      'createdBy': createdBy,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'employeeName': employeeName,
      'employeeNameArabic': employeeNameArabic,
      'creatorName': creatorName,
    };
  }

  /// إجمالي المكاسب
  double get totalEarnings => basicSalary + allowances + bonuses + incentives + financialAssistance;

  /// إجمالي الخصومات
  double get totalDeductions => deductions + advanceDeductions;

  /// الحصول على حالة الدفع بالعربية
  String get paymentStatusArabic {
    switch (paymentStatus) {
      case 'pending':
        return 'في الانتظار';
      case 'paid':
        return 'مدفوع';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  /// التحقق من كون الراتب مدفوع
  bool get isPaid => paymentStatus == 'paid';

  /// التحقق من كون الراتب في الانتظار
  bool get isPending => paymentStatus == 'pending';

  /// التحقق من كون الراتب ملغي
  bool get isCancelled => paymentStatus == 'cancelled';

  /// الحصول على فترة الراتب منسقة
  String get payPeriodFormatted {
    final startMonth = payPeriodStart.month.toString().padLeft(2, '0');
    final startDay = payPeriodStart.day.toString().padLeft(2, '0');
    final endMonth = payPeriodEnd.month.toString().padLeft(2, '0');
    final endDay = payPeriodEnd.day.toString().padLeft(2, '0');
    
    return '${payPeriodStart.year}/$startMonth/$startDay - ${payPeriodEnd.year}/$endMonth/$endDay';
  }

  /// الحصول على الشهر والسنة
  String get monthYear {
    final months = [
      '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${months[payPeriodStart.month]} ${payPeriodStart.year}';
  }

  @override
  String toString() {
    return 'EmployeePayroll(id: $id, employeeId: $employeeId, period: $payPeriodFormatted, netSalary: $netSalary)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmployeePayroll && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// DTO لإنشاء كشف راتب جديد
class CreatePayrollDto {
  final int employeeId;
  final DateTime payPeriodStart;
  final DateTime payPeriodEnd;
  final double basicSalary;
  final double allowances;
  final double bonuses;
  final double incentives;
  final double deductions;
  final double advanceDeductions;
  final double financialAssistance;
  final String? notes;
  final int? createdBy;

  const CreatePayrollDto({
    required this.employeeId,
    required this.payPeriodStart,
    required this.payPeriodEnd,
    required this.basicSalary,
    this.allowances = 0,
    this.bonuses = 0,
    this.incentives = 0,
    this.deductions = 0,
    this.advanceDeductions = 0,
    this.financialAssistance = 0,
    this.notes,
    this.createdBy,
  });

  factory CreatePayrollDto.fromJson(Map<String, dynamic> json) {
    return CreatePayrollDto(
      employeeId: json['employeeId'] as int,
      payPeriodStart: DateTime.parse(json['payPeriodStart'] as String),
      payPeriodEnd: DateTime.parse(json['payPeriodEnd'] as String),
      basicSalary: (json['basicSalary'] as num).toDouble(),
      allowances: (json['allowances'] as num?)?.toDouble() ?? 0,
      bonuses: (json['bonuses'] as num?)?.toDouble() ?? 0,
      incentives: (json['incentives'] as num?)?.toDouble() ?? 0,
      deductions: (json['deductions'] as num?)?.toDouble() ?? 0,
      advanceDeductions: (json['advanceDeductions'] as num?)?.toDouble() ?? 0,
      financialAssistance: (json['financialAssistance'] as num?)?.toDouble() ?? 0,
      notes: json['notes'] as String?,
      createdBy: json['createdBy'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employeeId': employeeId,
      'payPeriodStart': payPeriodStart.toIso8601String(),
      'payPeriodEnd': payPeriodEnd.toIso8601String(),
      'basicSalary': basicSalary,
      'allowances': allowances,
      'bonuses': bonuses,
      'incentives': incentives,
      'deductions': deductions,
      'advanceDeductions': advanceDeductions,
      'financialAssistance': financialAssistance,
      'notes': notes,
      'createdBy': createdBy,
    };
  }

  /// حساب الراتب الصافي
  double get netSalary {
    final totalEarnings = basicSalary + allowances + bonuses + incentives + financialAssistance;
    final totalDeductions = deductions + advanceDeductions;
    return totalEarnings - totalDeductions;
  }
}

/// DTO لتحديث كشف راتب
class UpdatePayrollDto {
  final DateTime? payPeriodStart;
  final DateTime? payPeriodEnd;
  final double? basicSalary;
  final double? allowances;
  final double? bonuses;
  final double? incentives;
  final double? deductions;
  final double? advanceDeductions;
  final double? financialAssistance;
  final String? notes;
  final int? updatedBy;

  const UpdatePayrollDto({
    this.payPeriodStart,
    this.payPeriodEnd,
    this.basicSalary,
    this.allowances,
    this.bonuses,
    this.incentives,
    this.deductions,
    this.advanceDeductions,
    this.financialAssistance,
    this.notes,
    this.updatedBy,
  });

  factory UpdatePayrollDto.fromJson(Map<String, dynamic> json) {
    return UpdatePayrollDto(
      payPeriodStart: json['payPeriodStart'] != null ? DateTime.parse(json['payPeriodStart'] as String) : null,
      payPeriodEnd: json['payPeriodEnd'] != null ? DateTime.parse(json['payPeriodEnd'] as String) : null,
      basicSalary: (json['basicSalary'] as num?)?.toDouble(),
      allowances: (json['allowances'] as num?)?.toDouble(),
      bonuses: (json['bonuses'] as num?)?.toDouble(),
      incentives: (json['incentives'] as num?)?.toDouble(),
      deductions: (json['deductions'] as num?)?.toDouble(),
      advanceDeductions: (json['advanceDeductions'] as num?)?.toDouble(),
      financialAssistance: (json['financialAssistance'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      updatedBy: json['updatedBy'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'payPeriodStart': payPeriodStart?.toIso8601String(),
      'payPeriodEnd': payPeriodEnd?.toIso8601String(),
      'basicSalary': basicSalary,
      'allowances': allowances,
      'bonuses': bonuses,
      'incentives': incentives,
      'deductions': deductions,
      'advanceDeductions': advanceDeductions,
      'financialAssistance': financialAssistance,
      'notes': notes,
      'updatedBy': updatedBy,
    };
  }
}

/// DTO لتأكيد دفع الراتب

class ConfirmPaymentDto {
  final DateTime paymentDate;
  final String? paymentMethod;
  final String? transactionReference;
  final String? notes;

  const ConfirmPaymentDto({
    required this.paymentDate,
    this.paymentMethod,
    this.transactionReference,
    this.notes,
  });

  factory ConfirmPaymentDto.fromJson(Map<String, dynamic> json) {
    return ConfirmPaymentDto(
      paymentDate: DateTime.parse(json['paymentDate'] as String),
      paymentMethod: json['paymentMethod'] as String?,
      transactionReference: json['transactionReference'] as String?,
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paymentDate': paymentDate.toIso8601String(),
      'paymentMethod': paymentMethod,
      'transactionReference': transactionReference,
      'notes': notes,
    };
  }
}

/// DTO لإحصائيات الرواتب

class PayrollStatisticsDto {
  final int totalPayrolls;
  final int paidPayrolls;
  final int pendingPayrolls;
  final int cancelledPayrolls;
  final double totalBasicSalaries;
  final double totalAllowances;
  final double totalBonuses;
  final double totalDeductions;
  final double totalNetSalaries;
  final double averageSalary;
  final Map<String, double> salariesByDepartment;
  final Map<String, int> payrollsByMonth;

  const PayrollStatisticsDto({
    required this.totalPayrolls,
    required this.paidPayrolls,
    required this.pendingPayrolls,
    required this.cancelledPayrolls,
    required this.totalBasicSalaries,
    required this.totalAllowances,
    required this.totalBonuses,
    required this.totalDeductions,
    required this.totalNetSalaries,
    required this.averageSalary,
    required this.salariesByDepartment,
    required this.payrollsByMonth,
  });

  factory PayrollStatisticsDto.fromJson(Map<String, dynamic> json) {
    return PayrollStatisticsDto(
      totalPayrolls: json['totalPayrolls'] ?? 0,
      paidPayrolls: json['paidPayrolls'] ?? 0,
      pendingPayrolls: json['pendingPayrolls'] ?? 0,
      cancelledPayrolls: json['cancelledPayrolls'] ?? 0,
      totalBasicSalaries: (json['totalAmount'] as num?)?.toDouble() ?? 0,
      totalAllowances: 0, // سيتم حسابها لاحقاً
      totalBonuses: 0, // سيتم حسابها لاحقاً
      totalDeductions: 0, // سيتم حسابها لاحقاً
      totalNetSalaries: (json['totalAmount'] as num?)?.toDouble() ?? 0,
      averageSalary: (json['averagePayroll'] as num?)?.toDouble() ?? 0,
      salariesByDepartment: <String, double>{}, // سيتم إضافتها لاحقاً
      payrollsByMonth: <String, int>{}, // سيتم إضافتها لاحقاً
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalPayrolls': totalPayrolls,
      'paidPayrolls': paidPayrolls,
      'pendingPayrolls': pendingPayrolls,
      'cancelledPayrolls': cancelledPayrolls,
      'totalBasicSalaries': totalBasicSalaries,
      'totalAllowances': totalAllowances,
      'totalBonuses': totalBonuses,
      'totalDeductions': totalDeductions,
      'totalNetSalaries': totalNetSalaries,
      'averageSalary': averageSalary,
      'salariesByDepartment': salariesByDepartment,
      'payrollsByMonth': payrollsByMonth,
    };
  }
}

/// تعداد حالات الدفع
enum PaymentStatus {
  pending('pending', 'في الانتظار'),
  paid('paid', 'مدفوع'),
  cancelled('cancelled', 'ملغي');

  const PaymentStatus(this.value, this.arabicName);

  final String value;
  final String arabicName;

  static PaymentStatus fromString(String value) {
    return PaymentStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => PaymentStatus.pending,
    );
  }
}

/// تعداد أنواع المكافآت والبدلات
enum PayrollComponent {
  basicSalary('basic_salary', 'الراتب الأساسي'),
  allowances('allowances', 'البدلات'),
  bonuses('bonuses', 'المكافآت'),
  incentives('incentives', 'الحوافز'),
  deductions('deductions', 'الخصومات'),
  advanceDeductions('advance_deductions', 'خصم السلف'),
  financialAssistance('financial_assistance', 'المساعدات المالية');

  const PayrollComponent(this.value, this.arabicName);

  final String value;
  final String arabicName;

  static PayrollComponent fromString(String value) {
    return PayrollComponent.values.firstWhere(
      (component) => component.value == value,
      orElse: () => PayrollComponent.basicSalary,
    );
  }
}
