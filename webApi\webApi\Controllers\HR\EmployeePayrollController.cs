using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.HR;
using webApi.Services;
using System.ComponentModel.DataAnnotations;

namespace webApi.Controllers.HR
{
    /// <summary>
    /// Controller لإدارة الرواتب والمكافآت للموظفين
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class EmployeePayrollController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<EmployeePayrollController> _logger;
        private readonly ILoggingService _loggingService;

        public EmployeePayrollController(
            TasksDbContext context, 
            ILogger<EmployeePayrollController> logger, 
            ILoggingService loggingService)
        {
            _context = context;
            _logger = logger;
            _loggingService = loggingService;
        }

        /// <summary>
        /// الحصول على جميع سجلات الرواتب مع الفلترة والترقيم
        /// </summary>
        /// <param name="page">رقم الصفحة</param>
        /// <param name="pageSize">حجم الصفحة</param>
        /// <param name="search">البحث</param>
        /// <param name="status">حالة الدفع</param>
        /// <param name="month">الشهر</param>
        /// <param name="year">السنة</param>
        /// <param name="employeeId">معرف الموظف</param>
        /// <returns>قائمة سجلات الرواتب</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<EmployeePayroll>>> GetAllPayrolls(
            int page = 1,
            int pageSize = 20,
            string? search = null,
            string? status = null,
            int? month = null,
            int? year = null,
            int? employeeId = null)
        {
            try
            {
                var query = _context.EmployeePayrolls
                    .Include(p => p.Employee)
                    .Include(p => p.Creator)
                    .AsQueryable();

                // تطبيق الفلاتر
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p =>
                        p.Employee.Name.Contains(search) ||
                        p.Employee.FullNameArabic.Contains(search) ||
                        p.Notes.Contains(search));
                }

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(p => p.PaymentStatus == status);
                }

                if (month.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Month == month.Value);
                }

                if (year.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Year == year.Value);
                }

                if (employeeId.HasValue)
                {
                    query = query.Where(p => p.EmployeeId == employeeId.Value);
                }

                // تطبيق الترقيم
                var totalCount = await query.CountAsync();
                var payrolls = await query
                    .OrderByDescending(p => p.PayPeriodStart)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new
                    {
                        id = p.Id,
                        employeeId = p.EmployeeId,
                        payPeriodStart = p.PayPeriodStart,
                        payPeriodEnd = p.PayPeriodEnd,
                        basicSalary = p.BasicSalary,
                        allowances = p.Allowances,
                        bonuses = p.Bonuses,
                        incentives = p.Incentives,
                        deductions = p.Deductions,
                        advanceDeductions = p.AdvanceDeductions,
                        financialAssistance = p.FinancialAssistance,
                        netSalary = p.NetSalary,
                        paymentDate = p.PaymentDate,
                        paymentStatus = p.PaymentStatus,
                        notes = p.Notes,
                        createdBy = p.CreatedBy,
                        createdAt = p.CreatedAt,
                        updatedAt = p.UpdatedAt,
                        // إضافة أسماء الموظفين
                        employeeName = p.Employee.Name,
                        employeeNameArabic = p.Employee.FullNameArabic,
                        creatorName = p.Creator.Name
                    })
                    .ToListAsync();

                // إضافة معلومات الترقيم في الرأس
                Response.Headers.Add("X-Total-Count", totalCount.ToString());
                Response.Headers.Add("X-Page", page.ToString());
                Response.Headers.Add("X-Page-Size", pageSize.ToString());

                return Ok(payrolls);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على سجلات الرواتب");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على سجلات الرواتب لموظف محدد
        /// </summary>
        /// <param name="employeeId">معرف الموظف</param>
        /// <param name="year">السنة (اختياري)</param>
        /// <param name="month">الشهر (اختياري)</param>
        /// <returns>قائمة سجلات الرواتب</returns>
        [HttpGet("employee/{employeeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<EmployeePayroll>>> GetEmployeePayrolls(
            int employeeId,
            int? year = null,
            int? month = null)
        {
            try
            {
                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(employeeId);
                if (employee == null || employee.IsDeleted)
                {
                    return NotFound(new { message = "الموظف غير موجود" });
                }

                var query = _context.EmployeePayrolls
                    .Include(p => p.Employee)
                    .Include(p => p.Creator)
                    .Where(p => p.EmployeeId == employeeId);

                // تطبيق فلتر السنة
                if (year.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Year == year.Value);
                }

                // تطبيق فلتر الشهر
                if (month.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Month == month.Value);
                }

                var payrolls = await query
                    .OrderByDescending(p => p.PayPeriodStart)
                    .Select(p => new
                    {
                        id = p.Id,
                        employeeId = p.EmployeeId,
                        payPeriodStart = p.PayPeriodStart,
                        payPeriodEnd = p.PayPeriodEnd,
                        basicSalary = p.BasicSalary,
                        allowances = p.Allowances,
                        bonuses = p.Bonuses,
                        incentives = p.Incentives,
                        deductions = p.Deductions,
                        advanceDeductions = p.AdvanceDeductions,
                        financialAssistance = p.FinancialAssistance,
                        netSalary = p.NetSalary,
                        paymentDate = p.PaymentDate,
                        paymentStatus = p.PaymentStatus,
                        notes = p.Notes,
                        createdBy = p.CreatedBy,
                        createdAt = p.CreatedAt,
                        updatedAt = p.UpdatedAt,
                        // إضافة أسماء الموظفين
                        employeeName = p.Employee.Name,
                        employeeNameArabic = p.Employee.FullNameArabic,
                        creatorName = p.Creator.Name
                    })
                    .ToListAsync();

                return Ok(payrolls);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على سجلات الرواتب للموظف {EmployeeId}", employeeId);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على سجل راتب محدد
        /// </summary>
        /// <param name="id">معرف سجل الراتب</param>
        /// <returns>بيانات سجل الراتب</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<object>> GetPayroll(int id)
        {
            try
            {
                var payroll = await _context.EmployeePayrolls
                    .Include(p => p.Employee)
                    .Include(p => p.Creator)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (payroll == null)
                {
                    return NotFound(new { message = "سجل الراتب غير موجود" });
                }

                // إنشاء كائن مع أسماء الموظفين
                var result = new
                {
                    id = payroll.Id,
                    employeeId = payroll.EmployeeId,
                    payPeriodStart = payroll.PayPeriodStart,
                    payPeriodEnd = payroll.PayPeriodEnd,
                    basicSalary = payroll.BasicSalary,
                    allowances = payroll.Allowances,
                    bonuses = payroll.Bonuses,
                    incentives = payroll.Incentives,
                    deductions = payroll.Deductions,
                    advanceDeductions = payroll.AdvanceDeductions,
                    financialAssistance = payroll.FinancialAssistance,
                    netSalary = payroll.NetSalary,
                    paymentDate = payroll.PaymentDate,
                    paymentStatus = payroll.PaymentStatus,
                    notes = payroll.Notes,
                    createdBy = payroll.CreatedBy,
                    createdAt = payroll.CreatedAt,
                    updatedAt = payroll.UpdatedAt,
                    // إضافة أسماء الموظفين
                    employeeName = payroll.Employee?.Name,
                    employeeNameArabic = payroll.Employee?.FullNameArabic,
                    creatorName = payroll.Creator?.Name
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على سجل الراتب {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// إنشاء سجل راتب جديد
        /// </summary>
        /// <param name="dto">بيانات سجل الراتب</param>
        /// <returns>سجل الراتب المُنشأ</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EmployeePayroll>> CreatePayroll([FromBody] CreatePayrollDto dto)
        {
            try
            {
                // التحقق من وجود الموظف
                var employee = await _context.Users.FindAsync(dto.EmployeeId);
                if (employee == null || employee.IsDeleted)
                {
                    return NotFound(new { message = "الموظف غير موجود" });
                }

                // التحقق من وجود المنشئ
                var creator = await _context.Users.FindAsync(dto.CreatedBy);
                if (creator == null || creator.IsDeleted)
                {
                    return NotFound(new { message = "المستخدم المنشئ غير موجود" });
                }

                // التحقق من صحة التواريخ
                if (dto.PayPeriodEnd <= dto.PayPeriodStart)
                {
                    return BadRequest(new { message = "تاريخ نهاية الفترة يجب أن يكون بعد تاريخ البداية" });
                }

                // التحقق من عدم وجود راتب لنفس الفترة
                var existingPayroll = await _context.EmployeePayrolls
                    .AnyAsync(p => p.EmployeeId == dto.EmployeeId && 
                                  p.PayPeriodStart == dto.PayPeriodStart && 
                                  p.PayPeriodEnd == dto.PayPeriodEnd);

                if (existingPayroll)
                {
                    return BadRequest(new { message = "يوجد سجل راتب لنفس الفترة مسبقاً" });
                }

                // حساب الراتب الصافي
                var totalEarnings = dto.BasicSalary + dto.Allowances + dto.Bonuses + dto.Incentives + dto.FinancialAssistance;
                var totalDeductions = dto.Deductions + dto.AdvanceDeductions;
                var netSalary = totalEarnings - totalDeductions;

                var payroll = new EmployeePayroll
                {
                    EmployeeId = dto.EmployeeId,
                    PayPeriodStart = dto.PayPeriodStart,
                    PayPeriodEnd = dto.PayPeriodEnd,
                    BasicSalary = dto.BasicSalary,
                    Allowances = dto.Allowances,
                    Bonuses = dto.Bonuses,
                    Incentives = dto.Incentives,
                    Deductions = dto.Deductions,
                    AdvanceDeductions = dto.AdvanceDeductions,
                    FinancialAssistance = dto.FinancialAssistance,
                    NetSalary = netSalary,
                    PaymentDate = dto.PaymentDate,
                    PaymentStatus = dto.PaymentStatus,
                    Notes = dto.Notes,
                    CreatedBy = dto.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.EmployeePayrolls.Add(payroll);
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "إنشاء سجل راتب",
                    "EmployeePayroll",
                    payroll.Id,
                    dto.EmployeeId,
                    $"تم إنشاء سجل راتب للفترة من {dto.PayPeriodStart:yyyy/MM/dd} إلى {dto.PayPeriodEnd:yyyy/MM/dd} بمبلغ {netSalary:C}"
                );

                // إحضار البيانات مع أسماء الموظفين
                var createdPayroll = await _context.EmployeePayrolls
                    .Include(p => p.Employee)
                    .Include(p => p.Creator)
                    .Where(p => p.Id == payroll.Id)
                    .Select(p => new
                    {
                        id = p.Id,
                        employeeId = p.EmployeeId,
                        payPeriodStart = p.PayPeriodStart,
                        payPeriodEnd = p.PayPeriodEnd,
                        basicSalary = p.BasicSalary,
                        allowances = p.Allowances,
                        bonuses = p.Bonuses,
                        incentives = p.Incentives,
                        deductions = p.Deductions,
                        advanceDeductions = p.AdvanceDeductions,
                        financialAssistance = p.FinancialAssistance,
                        netSalary = p.NetSalary,
                        paymentDate = p.PaymentDate,
                        paymentStatus = p.PaymentStatus,
                        notes = p.Notes,
                        createdBy = p.CreatedBy,
                        createdAt = p.CreatedAt,
                        updatedAt = p.UpdatedAt,
                        // إضافة أسماء الموظفين
                        employeeName = p.Employee.Name,
                        employeeNameArabic = p.Employee.FullNameArabic,
                        creatorName = p.Creator.Name
                    })
                    .FirstOrDefaultAsync();

                return CreatedAtAction(nameof(GetPayroll), new { id = payroll.Id }, createdPayroll);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في إنشاء سجل الراتب");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تحديث سجل راتب
        /// </summary>
        /// <param name="id">معرف سجل الراتب</param>
        /// <param name="dto">البيانات المحدثة</param>
        /// <returns>سجل الراتب المحدث</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<EmployeePayroll>> UpdatePayroll(int id, [FromBody] UpdatePayrollDto dto)
        {
            try
            {
                var payroll = await _context.EmployeePayrolls.FindAsync(id);
                if (payroll == null)
                {
                    return NotFound(new { message = "سجل الراتب غير موجود" });
                }

                // التحقق من إمكانية التعديل (لا يمكن تعديل الراتب المدفوع)
                if (payroll.PaymentStatus == "paid")
                {
                    return BadRequest(new { message = "لا يمكن تعديل راتب مدفوع" });
                }

                // التحقق من صحة التواريخ
                if (dto.PayPeriodEnd <= dto.PayPeriodStart)
                {
                    return BadRequest(new { message = "تاريخ نهاية الفترة يجب أن يكون بعد تاريخ البداية" });
                }

                // حساب الراتب الصافي الجديد
                var totalEarnings = dto.BasicSalary + dto.Allowances + dto.Bonuses + dto.Incentives + dto.FinancialAssistance;
                var totalDeductions = dto.Deductions + dto.AdvanceDeductions;
                var netSalary = totalEarnings - totalDeductions;

                // تحديث البيانات
                payroll.PayPeriodStart = dto.PayPeriodStart;
                payroll.PayPeriodEnd = dto.PayPeriodEnd;
                payroll.BasicSalary = dto.BasicSalary;
                payroll.Allowances = dto.Allowances;
                payroll.Bonuses = dto.Bonuses;
                payroll.Incentives = dto.Incentives;
                payroll.Deductions = dto.Deductions;
                payroll.AdvanceDeductions = dto.AdvanceDeductions;
                payroll.FinancialAssistance = dto.FinancialAssistance;
                payroll.NetSalary = netSalary;
                payroll.PaymentDate = dto.PaymentDate;
                payroll.PaymentStatus = dto.PaymentStatus;
                payroll.Notes = dto.Notes;
                payroll.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "تحديث سجل راتب",
                    "EmployeePayroll",
                    id,
                    payroll.EmployeeId,
                    $"تم تحديث سجل الراتب للفترة من {dto.PayPeriodStart:yyyy/MM/dd} إلى {dto.PayPeriodEnd:yyyy/MM/dd}"
                );

                return Ok(payroll);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في تحديث سجل الراتب {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// حذف سجل راتب
        /// </summary>
        /// <param name="id">معرف سجل الراتب</param>
        /// <returns>نتيجة العملية</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> DeletePayroll(int id)
        {
            try
            {
                var payroll = await _context.EmployeePayrolls.FindAsync(id);
                if (payroll == null)
                {
                    return NotFound(new { message = "سجل الراتب غير موجود" });
                }

                // التحقق من إمكانية الحذف (لا يمكن حذف الراتب المدفوع)
                if (payroll.PaymentStatus == "paid")
                {
                    return BadRequest(new { message = "لا يمكن حذف راتب مدفوع" });
                }

                _context.EmployeePayrolls.Remove(payroll);
                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "حذف سجل راتب",
                    "EmployeePayroll",
                    id,
                    payroll.EmployeeId,
                    $"تم حذف سجل الراتب للفترة من {payroll.PayPeriodStart:yyyy/MM/dd} إلى {payroll.PayPeriodEnd:yyyy/MM/dd}"
                );

                return Ok(new { message = "تم حذف سجل الراتب بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في حذف سجل الراتب {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الرواتب
        /// </summary>
        /// <param name="month">الشهر (اختياري)</param>
        /// <param name="year">السنة (اختياري)</param>
        /// <param name="departmentId">معرف القسم (اختياري)</param>
        /// <returns>إحصائيات الرواتب</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetPayrollStatistics(
            int? month = null,
            int? year = null,
            int? departmentId = null)
        {
            try
            {
                var query = _context.EmployeePayrolls.AsQueryable();

                // تطبيق الفلاتر
                if (month.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Month == month.Value);
                }

                if (year.HasValue)
                {
                    query = query.Where(p => p.PayPeriodStart.Year == year.Value);
                }

                if (departmentId.HasValue)
                {
                    query = query.Where(p => p.Employee.DepartmentId == departmentId.Value);
                }

                // حساب الإحصائيات
                var totalPayrolls = await query.CountAsync();
                var paidPayrolls = await query.CountAsync(p => p.PaymentStatus == "paid");
                var pendingPayrolls = await query.CountAsync(p => p.PaymentStatus == "pending");
                var cancelledPayrolls = await query.CountAsync(p => p.PaymentStatus == "cancelled");

                var totalAmount = await query.SumAsync(p => p.NetSalary);
                var paidAmount = await query.Where(p => p.PaymentStatus == "paid").SumAsync(p => p.NetSalary);
                var pendingAmount = await query.Where(p => p.PaymentStatus == "pending").SumAsync(p => p.NetSalary);

                var statistics = new
                {
                    TotalPayrolls = totalPayrolls,
                    PaidPayrolls = paidPayrolls,
                    PendingPayrolls = pendingPayrolls,
                    CancelledPayrolls = cancelledPayrolls,
                    TotalAmount = totalAmount,
                    PaidAmount = paidAmount,
                    PendingAmount = pendingAmount,
                    AveragePayroll = totalPayrolls > 0 ? totalAmount / totalPayrolls : 0
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في الحصول على إحصائيات الرواتب");
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }

        /// <summary>
        /// تأكيد دفع الراتب
        /// </summary>
        /// <param name="id">معرف سجل الراتب</param>
        /// <param name="dto">بيانات تأكيد الدفع</param>
        /// <returns>نتيجة العملية</returns>
        [HttpPost("{id}/confirm-payment")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult> ConfirmPayment(int id, [FromBody] ConfirmPaymentDto dto)
        {
            try
            {
                var payroll = await _context.EmployeePayrolls.FindAsync(id);
                if (payroll == null)
                {
                    return NotFound(new { message = "سجل الراتب غير موجود" });
                }

                if (payroll.PaymentStatus == "paid")
                {
                    return BadRequest(new { message = "الراتب مدفوع مسبقاً" });
                }

                payroll.PaymentStatus = "paid";
                payroll.PaymentDate = dto.PaymentDate ?? DateTime.Now;
                payroll.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                // تسجيل العملية
                await _loggingService.LogActivityAsync(
                    "تأكيد دفع راتب",
                    "EmployeePayroll",
                    id,
                    payroll.EmployeeId,
                    $"تم تأكيد دفع الراتب بمبلغ {payroll.NetSalary:C} بتاريخ {payroll.PaymentDate:yyyy/MM/dd}"
                );

                return Ok(new { message = "تم تأكيد دفع الراتب بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في تأكيد دفع الراتب {Id}", id);
                return StatusCode(500, new { message = "خطأ داخلي في الخادم" });
            }
        }
    }

    // DTOs للرواتب
    public class CreatePayrollDto
    {
        [Required(ErrorMessage = "معرف الموظف مطلوب")]
        public int EmployeeId { get; set; }

        [Required(ErrorMessage = "تاريخ بداية الفترة مطلوب")]
        public DateTime PayPeriodStart { get; set; }

        [Required(ErrorMessage = "تاريخ نهاية الفترة مطلوب")]
        public DateTime PayPeriodEnd { get; set; }

        [Required(ErrorMessage = "الراتب الأساسي مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "الراتب الأساسي يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal BasicSalary { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "البدلات يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal Allowances { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "المكافآت يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal Bonuses { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "الحوافز يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal Incentives { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "الخصومات يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal Deductions { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "خصومات السلف يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal AdvanceDeductions { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "المساعدات المالية يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal FinancialAssistance { get; set; } = 0;

        public DateTime? PaymentDate { get; set; }

        [StringLength(20, ErrorMessage = "حالة الدفع يجب أن تكون أقل من 20 حرف")]
        public string PaymentStatus { get; set; } = "pending";

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        [Required(ErrorMessage = "معرف المنشئ مطلوب")]
        public int CreatedBy { get; set; }
    }

    public class UpdatePayrollDto : CreatePayrollDto
    {
        // يرث جميع الخصائص من CreatePayrollDto
    }

    public class ConfirmPaymentDto
    {
        public DateTime? PaymentDate { get; set; }
    }
}
