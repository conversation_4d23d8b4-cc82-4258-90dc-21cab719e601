import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/advance_model.dart';
import '../services/advance_api_service.dart';

/// متحكم إدارة السلف والمساعدات المالية
class AdvanceController extends GetxController {
  late final AdvanceApiService _apiService;

  // Observable variables
  final RxList<EmployeeAdvance> advances = <EmployeeAdvance>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxString error = ''.obs;
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalRecords = 0.obs;
  final RxBool hasMoreData = true.obs;
  final Rx<AdvanceStatisticsDto?> statistics = Rx<AdvanceStatisticsDto?>(null);

  // Filter variables
  final RxString searchTerm = ''.obs;
  final RxString selectedStatus = ''.obs;
  final RxString selectedAdvanceType = ''.obs;
  final RxInt selectedDepartmentId = 0.obs;

  // Selected advance for details
  final Rx<EmployeeAdvance?> selectedAdvance = Rx<EmployeeAdvance?>(null);

  // Constants
  static const int pageSize = 20;

  @override
  void onInit() {
    super.onInit();
    _initializeApiService();
    loadAdvances();
    loadStatistics();
  }

  void _initializeApiService() {
    try {
      _apiService = Get.find<AdvanceApiService>();
    } catch (e) {
      _apiService = AdvanceApiService();
      Get.put(_apiService);
    }
  }

  /// تحميل السلف مع الفلترة والترقيم
  Future<void> loadAdvances({bool refresh = false}) async {
    try {
      if (refresh) {
        currentPage.value = 1;
        hasMoreData.value = true;
        advances.clear();
      }

      if (!hasMoreData.value && !refresh) return;

      if (currentPage.value == 1) {
        isLoading.value = true;
      } else {
        isLoadingMore.value = true;
      }

      error.value = '';

      final result = await _apiService.getAllAdvances(
        page: currentPage.value,
        pageSize: pageSize,
        status: selectedStatus.value.isEmpty ? null : selectedStatus.value,
        advanceType: selectedAdvanceType.value.isEmpty ? null : selectedAdvanceType.value,
        departmentId: selectedDepartmentId.value == 0 ? null : selectedDepartmentId.value,
        searchTerm: searchTerm.value.isEmpty ? null : searchTerm.value,
      );

      final newAdvances = result['advances'] as List<EmployeeAdvance>;
      totalRecords.value = result['totalRecords'];
      totalPages.value = result['totalPages'];

      if (refresh) {
        advances.value = newAdvances;
      } else {
        advances.addAll(newAdvances);
      }

      hasMoreData.value = currentPage.value < totalPages.value;
      if (hasMoreData.value) {
        currentPage.value++;
      }

    } catch (e) {
      error.value = 'خطأ في تحميل السلف: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        error.value,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  /// تحميل المزيد من السلف
  Future<void> loadMoreAdvances() async {
    if (!isLoadingMore.value && hasMoreData.value) {
      await loadAdvances();
    }
  }

  /// تحميل إحصائيات السلف
  Future<void> loadStatistics() async {
    try {
      final stats = await _apiService.getAdvanceStatistics();
      statistics.value = stats;
    } catch (e) {
      debugPrint('خطأ في تحميل إحصائيات السلف: $e');
    }
  }

  /// إنشاء سلفة جديدة
  Future<bool> createAdvance(CreateAdvanceDto advanceData) async {
    try {
      isLoading.value = true;
      error.value = '';

      final newAdvance = await _apiService.createAdvance(advanceData);
      advances.insert(0, newAdvance);
      totalRecords.value++;

      Get.snackbar(
        'نجح',
        'تم إنشاء طلب السلفة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // إعادة تحميل الإحصائيات
      loadStatistics();

      return true;
    } catch (e) {
      error.value = 'خطأ في إنشاء السلفة: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        'فشل في إنشاء طلب السلفة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// الموافقة على سلفة أو رفضها
  Future<bool> approveAdvance(int advanceId, ApproveAdvanceDto approvalData) async {
    try {
      isLoading.value = true;
      error.value = '';

      await _apiService.approveAdvance(advanceId, approvalData);

      // تحديث حالة السلفة في القائمة
      final index = advances.indexWhere((advance) => advance.id == advanceId);
      if (index != -1) {
        final updatedAdvance = advances[index].copyWith(
          status: approvalData.status,
          approvalDate: approvalData.approvalDate ?? DateTime.now(),
          startDeductionDate: approvalData.startDeductionDate,
        );
        advances[index] = updatedAdvance;
      }

      // تحديث السلفة المحددة إذا كانت نفسها
      if (selectedAdvance.value?.id == advanceId) {
        selectedAdvance.value = selectedAdvance.value?.copyWith(
          status: approvalData.status,
          approvalDate: approvalData.approvalDate ?? DateTime.now(),
          startDeductionDate: approvalData.startDeductionDate,
        );
      }

      Get.snackbar(
        'نجح',
        approvalData.status == 'approved' ? 'تم اعتماد السلفة بنجاح' : 'تم رفض السلفة',
        backgroundColor: approvalData.status == 'approved' ? Colors.green : Colors.orange,
        colorText: Colors.white,
        icon: Icon(
          approvalData.status == 'approved' ? Icons.check_circle : Icons.cancel,
          color: Colors.white,
        ),
      );

      // إعادة تحميل الإحصائيات
      loadStatistics();

      return true;
    } catch (e) {
      error.value = 'خطأ في معالجة السلفة: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        'فشل في معالجة السلفة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تسجيل دفعة للسلفة
  Future<bool> recordPayment(int advanceId, AdvancePaymentDto paymentData) async {
    try {
      isLoading.value = true;
      error.value = '';

      final result = await _apiService.recordPayment(advanceId, paymentData);

      // تحديث السلفة في القائمة
      final index = advances.indexWhere((advance) => advance.id == advanceId);
      if (index != -1) {
        final updatedAdvance = advances[index].copyWith(
          remainingAmount: result['remainingAmount']?.toDouble() ?? advances[index].remainingAmount,
          status: result['isCompleted'] == true ? 'completed' : advances[index].status,
        );
        advances[index] = updatedAdvance;
      }

      // تحديث السلفة المحددة إذا كانت نفسها
      if (selectedAdvance.value?.id == advanceId) {
        selectedAdvance.value = selectedAdvance.value?.copyWith(
          remainingAmount: result['remainingAmount']?.toDouble() ?? selectedAdvance.value!.remainingAmount,
          status: result['isCompleted'] == true ? 'completed' : selectedAdvance.value!.status,
        );
      }

      Get.snackbar(
        'نجح',
        'تم تسجيل الدفعة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // إعادة تحميل الإحصائيات
      loadStatistics();

      return true;
    } catch (e) {
      error.value = 'خطأ في تسجيل الدفعة: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        'فشل في تسجيل الدفعة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف سلفة
  Future<bool> deleteAdvance(int advanceId) async {
    try {
      isLoading.value = true;
      error.value = '';

      await _apiService.deleteAdvance(advanceId);

      // إزالة السلفة من القائمة
      advances.removeWhere((advance) => advance.id == advanceId);
      totalRecords.value--;

      // إزالة السلفة المحددة إذا كانت نفسها
      if (selectedAdvance.value?.id == advanceId) {
        selectedAdvance.value = null;
      }

      Get.snackbar(
        'نجح',
        'تم حذف السلفة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // إعادة تحميل الإحصائيات
      loadStatistics();

      return true;
    } catch (e) {
      error.value = 'خطأ في حذف السلفة: ${e.toString()}';
      Get.snackbar(
        'خطأ',
        'فشل في حذف السلفة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديد سلفة للعرض
  void selectAdvance(EmployeeAdvance advance) {
    selectedAdvance.value = advance;
  }

  /// البحث في السلف
  void searchAdvances(String term) {
    searchTerm.value = term;
    loadAdvances(refresh: true);
  }

  /// تطبيق فلتر الحالة
  void filterByStatus(String status) {
    selectedStatus.value = status;
    loadAdvances(refresh: true);
  }

  /// تطبيق فلتر نوع السلفة
  void filterByAdvanceType(String advanceType) {
    selectedAdvanceType.value = advanceType;
    loadAdvances(refresh: true);
  }

  /// تطبيق فلتر القسم
  void filterByDepartment(int departmentId) {
    selectedDepartmentId.value = departmentId;
    loadAdvances(refresh: true);
  }

  /// إعادة تعيين الفلاتر
  void resetFilters() {
    searchTerm.value = '';
    selectedStatus.value = '';
    selectedAdvanceType.value = '';
    selectedDepartmentId.value = 0;
    loadAdvances(refresh: true);
  }

  /// الحصول على أنواع السلف
  List<Map<String, String>> getAdvanceTypes() {
    return _apiService.getAdvanceTypes();
  }

  /// الحصول على حالات السلف
  List<Map<String, String>> getAdvanceStatuses() {
    return _apiService.getAdvanceStatuses();
  }

  /// تحديث السلفة المحددة
  Future<void> refreshSelectedAdvance() async {
    if (selectedAdvance.value != null) {
      try {
        final updatedAdvance = await _apiService.getAdvance(selectedAdvance.value!.id);
        selectedAdvance.value = updatedAdvance;
        
        // تحديث السلفة في القائمة أيضاً
        final index = advances.indexWhere((advance) => advance.id == updatedAdvance.id);
        if (index != -1) {
          advances[index] = updatedAdvance;
        }
      } catch (e) {
        debugPrint('خطأ في تحديث السلفة المحددة: $e');
      }
    }
  }
}
