import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../controllers/advance_controller.dart';
import '../../models/advance_model.dart';
import '../../../../constants/app_colors.dart';
import '../../../../constants/app_styles.dart';
import '../../../../widgets/loading_indicator.dart';
import '../../../../widgets/custom_text_field.dart';
import '../../../../widgets/custom_dropdown.dart';
import '../../../../widgets/custom_date_picker.dart';
import '../../../../helpers/user_helper.dart';

/// شاشة إضافة سلفة جديدة
class AddAdvanceScreen extends StatefulWidget {
  const AddAdvanceScreen({super.key});

  @override
  State<AddAdvanceScreen> createState() => _AddAdvanceScreenState();
}

class _AddAdvanceScreenState extends State<AddAdvanceScreen> {
  final AdvanceController controller = Get.find<AdvanceController>();
  final _formKey = GlobalKey<FormState>();

  // Controllers للحقول
  final _amountController = TextEditingController();
  final _monthlyDeductionController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  // المتغيرات
  int? _selectedEmployeeId;
  String? _selectedAdvanceType;
  DateTime _requestDate = DateTime.now();
  bool _isLoading = false;

  // قائمة الموظفين (سيتم تحميلها من API)
  final RxList<Map<String, dynamic>> employees = <Map<String, dynamic>>[].obs;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
    
    // إذا كان المستخدم الحالي موظف، اختره تلقائياً
    final currentUser = UserHelper.getCurrentUser();
    if (currentUser != null) {
      _selectedEmployeeId = currentUser['id'];
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _monthlyDeductionController.dispose();
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الموظفين
  Future<void> _loadEmployees() async {
    try {
      // TODO: تحميل الموظفين من API
      // مؤقتاً سنستخدم بيانات وهمية
      employees.value = [
        {'id': 1, 'name': 'أحمد محمد', 'department': 'تقنية المعلومات'},
        {'id': 2, 'name': 'فاطمة علي', 'department': 'الموارد البشرية'},
        {'id': 3, 'name': 'محمد سالم', 'department': 'المحاسبة'},
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل الموظفين: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب سلفة جديدة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _submitForm,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: _isLoading ? Colors.grey : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات أساسية
                    _buildSectionCard(
                      title: 'المعلومات الأساسية',
                      icon: Icons.info_outline,
                      children: [
                        // اختيار الموظف
                        _buildEmployeeDropdown(),
                        const SizedBox(height: 16),
                        
                        // نوع السلفة
                        _buildAdvanceTypeDropdown(),
                        const SizedBox(height: 16),
                        
                        // تاريخ الطلب
                        _buildRequestDatePicker(),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // المعلومات المالية
                    _buildSectionCard(
                      title: 'المعلومات المالية',
                      icon: Icons.monetization_on,
                      children: [
                        // مبلغ السلفة
                        CustomTextField(
                          controller: _amountController,
                          label: 'مبلغ السلفة *',
                          hint: 'أدخل مبلغ السلفة بالريال السعودي',
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                          ],
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'مبلغ السلفة مطلوب';
                            }
                            final amount = double.tryParse(value);
                            if (amount == null || amount <= 0) {
                              return 'يجب أن يكون المبلغ أكبر من صفر';
                            }
                            if (amount > 100000) {
                              return 'المبلغ كبير جداً';
                            }
                            return null;
                          },
                          prefixIcon: Icons.attach_money,
                          suffixText: 'ر.س',
                        ),
                        const SizedBox(height: 16),
                        
                        // القسط الشهري (اختياري)
                        CustomTextField(
                          controller: _monthlyDeductionController,
                          label: 'القسط الشهري (اختياري)',
                          hint: 'أدخل مبلغ القسط الشهري',
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                          ],
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              final amount = double.tryParse(value);
                              if (amount == null || amount <= 0) {
                                return 'يجب أن يكون القسط أكبر من صفر';
                              }
                              final totalAmount = double.tryParse(_amountController.text);
                              if (totalAmount != null && amount > totalAmount) {
                                return 'القسط لا يمكن أن يكون أكبر من المبلغ الإجمالي';
                              }
                            }
                            return null;
                          },
                          prefixIcon: Icons.schedule,
                          suffixText: 'ر.س',
                        ),
                        
                        // معلومات إضافية عن القسط
                        if (_monthlyDeductionController.text.isNotEmpty)
                          _buildInstallmentInfo(),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // التفاصيل والملاحظات
                    _buildSectionCard(
                      title: 'التفاصيل والملاحظات',
                      icon: Icons.description,
                      children: [
                        // سبب السلفة
                        CustomTextField(
                          controller: _reasonController,
                          label: 'سبب السلفة',
                          hint: 'اذكر سبب طلب السلفة',
                          maxLines: 3,
                          validator: (value) {
                            if (value != null && value.length > 500) {
                              return 'السبب طويل جداً';
                            }
                            return null;
                          },
                          prefixIcon: Icons.help_outline,
                        ),
                        const SizedBox(height: 16),
                        
                        // ملاحظات إضافية
                        CustomTextField(
                          controller: _notesController,
                          label: 'ملاحظات إضافية',
                          hint: 'أي ملاحظات أو تفاصيل إضافية',
                          maxLines: 3,
                          validator: (value) {
                            if (value != null && value.length > 1000) {
                              return 'الملاحظات طويلة جداً';
                            }
                            return null;
                          },
                          prefixIcon: Icons.note,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // أزرار الإجراءات
                    _buildActionButtons(),
                    
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  /// بناء بطاقة قسم
  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppColors.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء قائمة اختيار الموظف
  Widget _buildEmployeeDropdown() {
    return Obx(() => CustomDropdown<int>(
      value: _selectedEmployeeId,
      label: 'الموظف *',
      hint: 'اختر الموظف',
      items: employees.map((employee) {
        return DropdownMenuItem<int>(
          value: employee['id'],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                employee['name'],
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                employee['department'],
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedEmployeeId = value;
        });
      },
      validator: (value) {
        if (value == null) {
          return 'يجب اختيار الموظف';
        }
        return null;
      },
      prefixIcon: Icons.person,
    ));
  }

  /// بناء قائمة اختيار نوع السلفة
  Widget _buildAdvanceTypeDropdown() {
    final advanceTypes = controller.getAdvanceTypes();
    
    return CustomDropdown<String>(
      value: _selectedAdvanceType,
      label: 'نوع السلفة *',
      hint: 'اختر نوع السلفة',
      items: advanceTypes.map((type) {
        return DropdownMenuItem<String>(
          value: type['value'],
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                type['label']!,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                type['description']!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedAdvanceType = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يجب اختيار نوع السلفة';
        }
        return null;
      },
      prefixIcon: Icons.category,
    );
  }

  /// بناء منتقي تاريخ الطلب
  Widget _buildRequestDatePicker() {
    return CustomDatePicker(
      label: 'تاريخ الطلب *',
      selectedDate: _requestDate,
      onDateSelected: (date) {
        setState(() {
          _requestDate = date;
        });
      },
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      validator: (date) {
        if (date == null) {
          return 'تاريخ الطلب مطلوب';
        }
        return null;
      },
      prefixIcon: Icons.calendar_today,
    );
  }
