import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import '../models/permission_models.dart';
import '../services/api/permissions_api_service.dart';
import '../controllers/auth_controller.dart';

/// خدمة الصلاحيات الموحدة - متوافقة مع ASP.NET Core API
class UnifiedPermissionService extends GetxService {
  final PermissionsApiService _apiService = PermissionsApiService();
  
  // قوائم الصلاحيات
  final RxList<Permission> _permissions = <Permission>[].obs;
  final RxMap<String, bool> _userPermissions = <String, bool>{}.obs;
  
  // Getters
  List<Permission> get permissions => _permissions;
  Map<String, bool> get userPermissions => _userPermissions;

  @override
  void onInit() {
    super.onInit();
    loadPermissions();
  }

  /// تحميل جميع الصلاحيات
  Future<void> loadPermissions() async {
    try {
      final permissions = await _apiService.getAllPermissions();
      _permissions.assignAll(permissions);
      debugPrint('تم تحميل ${permissions.length} صلاحية');
    } catch (e) {
      debugPrint('خطأ في تحميل الصلاحيات: $e');
    }
  }

  /// تحميل صلاحيات المستخدم
  Future<void> loadUserPermissions(int userId) async {
    try {
      debugPrint('🔄 تحميل صلاحيات المستخدم: $userId');

      // تحميل الصلاحيات المخصصة للمستخدم من قاعدة البيانات فقط
      final customPermissions = await _apiService.getCustomUserPermissions(userId);
      _userPermissions.clear();

      // إضافة الصلاحيات المخصصة النشطة فقط
      for (final userPermission in customPermissions) {
        if (userPermission.permission != null && userPermission.isActive && !userPermission.isDeleted) {
          _userPermissions[userPermission.permission!.name] = true;
        }
      }

      final activePermissions = customPermissions.where((p) => p.isActive && !p.isDeleted).length;
      debugPrint('✅ تم تحميل $activePermissions صلاحية نشطة من ${customPermissions.length} صلاحية مخصصة للمستخدم');

      // لا نحمل صلاحيات الدور الافتراضي - المستخدم يجب أن يملك صلاحيات مخصصة فقط
      if (activePermissions == 0) {
        debugPrint('⚠️ المستخدم $userId لا يملك أي صلاحيات نشطة - لن يتم تحميل صلاحيات افتراضية');
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحميل صلاحيات المستخدم: $e');
    }
  }

  /// التحقق من صلاحية المستخدم - يعتمد فقط على الصلاحيات الفعلية من قاعدة البيانات
  bool hasPermission(String permissionName) {
    // الاعتماد فقط على الصلاحيات الفعلية من قاعدة البيانات - لا توجد صلاحيات افتراضية
    var hasIt = _userPermissions[permissionName] ?? false;

    // إذا لم توجد الصلاحية، جرب البحث مع تنظيف المسافات
    if (!hasIt) {
      // البحث عن الصلاحية مع مسافات إضافية
      final permissionWithSpace = _userPermissions['$permissionName '];
      if (permissionWithSpace != null) {
        hasIt = permissionWithSpace;
        debugPrint('🔧 تم العثور على صلاحية مع مسافة إضافية: "$permissionName "');
      }

      // البحث في جميع المفاتيح مع تنظيف المسافات
      if (!hasIt) {
        for (final entry in _userPermissions.entries) {
          if (entry.key.trim() == permissionName.trim()) {
            hasIt = entry.value;
            debugPrint('🔧 تم العثور على صلاحية بعد تنظيف المسافات: "${entry.key}" -> "$permissionName"');
            break;
          }
        }
      }
    }

    // إرجاع false إذا لم توجد الصلاحية - لا توجد صلاحيات افتراضية أو منطق أدوار
    return hasIt;
  }

  /// التحقق من صلاحيات متعددة
  bool hasAnyPermission(List<String> permissionNames) {
    return permissionNames.any((permission) => hasPermission(permission));
  }

  /// التحقق من جميع الصلاحيات
  bool hasAllPermissions(List<String> permissionNames) {
    return permissionNames.every((permission) => hasPermission(permission));
  }

  // ========================================
  // 🎯 صلاحيات الواجهات الرئيسية
  // ========================================

  /// صلاحيات لوحة المعلومات
  bool canAccessDashboard() => hasPermission('dashboard.admin'); // تستخدم للتحكم في الوصول للوحة المعلومات الإدارية، زر لوحة تحكم المستخدم في dashboard_tab.dart، زر إضافة عنصر جديد FloatingActionButton، عنصر لوحة معلومات Monday.com في القائمة الجانبية app_drawer.dart، وفي أدوات التشخيص admin_debug_helper.dart
  bool canEditDashboard() => hasPermission('dashboard.edit'); // صلاحية تعديل لوحة المعلومات - لم يتم العثور على استخدامات مباشرة في المشروع حالياً، مخصصة لتعديل محتوى وإعدادات لوحة المعلومات
  bool canCustomizeDashboard() => hasPermission('dashboard.customize'); // تستخدم لتخصيص لوحة المعلومات: سحب وإفلات العناصر في monday_style_chart_widget.dart، النقر المزدوج على المخططات في draggable_chart_widget.dart، تعديل إعدادات المخططات في chart_detail_dialog.dart، والوصول للوحة المعلومات التقليدية في القائمة الجانبية app_drawer.dart
  bool canAddDashboardWidgets() => hasPermission('dashboard.add_widgets'); // تستخدم لإضافة عناصر جديدة للوحة المعلومات: FloatingActionButton في monday_dashboard_screen.dart لإضافة عناصر، زر "إضافة إلى لوحة المعلومات" في شريط أدوات التقارير report_toolbar.dart
  bool canRemoveDashboardWidgets() => hasPermission('dashboard.remove_widgets'); // تستخدم لإزالة وحذف عناصر من لوحة المعلومات: أزرار الحذف في dashboard_grid.dart، وظائف الحذف في customizable_dashboard_screen.dart، وحذف العناصر عبر dashboard_widgets_controller.dart
  bool canExportDashboard() => hasPermission('dashboard.export'); // تستخدم لتصدير لوحة المعلومات: زر التصدير في customizable_dashboard_screen.dart، تصدير المخططات في draggable_chart_widget.dart، وتصدير البيانات عبر dashboards_api_service.dart
  bool canShareDashboard() => hasPermission('dashboard.share'); // تستخدم لمشاركة لوحة المعلومات مع مستخدمين آخرين: وظائف المشاركة في dashboards_controller.dart، API المشاركة في dashboards_api_service.dart، ومشاركة لوحات المعلومات عبر DashboardsController.cs في الخادم

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canManageDashboard() => hasPermission('dashboard.manage');
  bool canAddDashboardWidget() => hasPermission('dashboard.add_widget');
  bool canExportCharts() => hasPermission('dashboard.export_charts');
  bool canUserDashboard() => hasPermission('dashboard.user_dashboard');

  /// صلاحيات المهام
  bool canAccessTasks() => hasPermission('tasks.view'); // تستخدم للتحكم في الوصول لقائمة المهام: التحقق من الوصول في home_screen.dart، زر التحديث في tasks_tab.dart، عنصر لوحة معلومات المهام في القائمة الجانبية app_drawer.dart، والتحقق من الصلاحيات في unified_permission_middleware.dart
  bool canCreateTask() => hasPermission('tasks.create'); // تستخدم لإنشاء مهام جديدة: FloatingActionButton في tasks_tab.dart و task_board_screen.dart، أزرار إضافة مهمة في مجموعات الحالة، زر إنشاء المهمة في create_task_screen.dart، والتحقق من الصلاحية قبل الانتقال لشاشة الإنشاء
  bool canEditTask() => hasPermission('tasks.edit'); // تستخدم لتعديل المهام: زر التعديل في task_detail_screen.dart، قائمة الخيارات في subtasks_tab.dart، السحب والإفلات في gantt_chart.dart، والتحقق من الصلاحية في task_detail_helpers.dart
  bool canDeleteTask() => hasPermission('tasks.delete'); // تستخدم لحذف المهام: قائمة الخيارات في subtasks_tab.dart، والتحقق من الصلاحية في task_detail_helpers.dart لحذف المهام والمهام الفرعية
  bool canAssignTask() => hasPermission('tasks.assign'); // تستخدم لتعيين المهام للمستخدمين: وظائف التعيين في task_controller.dart، وتحديث المكلف بالمهمة عبر assignTask()
  bool canUpdateOwnTask() => hasPermission('tasks.update_own'); // تستخدم لتحديث المهام الخاصة بالمستخدم - لم يتم العثور على استخدامات مباشرة حالياً
  bool canViewAllTasks() => hasPermission('tasks.view_all'); // تستخدم لعرض جميع المهام: التحقق في task_controller.dart، calendar_event_form.dart، وتحديد نطاق المهام المعروضة للمستخدم
  bool canTransferTask() => hasPermission('tasks.transfer'); // تستخدم لتحويل المهام: وظيفة transferTask في task_controller.dart لتحويل المهام بين المستخدمين مع تسجيل التاريخ
  bool canDuplicateTask() => hasPermission('tasks.duplicate'); // تستخدم لنسخ المهام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لإنشاء نسخ من المهام الموجودة
  bool canArchiveTask() => hasPermission('tasks.archive'); // تستخدم لأرشفة المهام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لنقل المهام للأرشيف
  bool canRestoreTask() => hasPermission('tasks.restore'); // تستخدم لاستعادة المهام من الأرشيف - لم يتم العثور على استخدامات مباشرة حالياً
  bool canExportTasks() => hasPermission('tasks.export'); // تستخدم لتصدير المهام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لتصدير قوائم المهام
  bool canImportTasks() => hasPermission('tasks.import'); // تستخدم لاستيراد المهام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لاستيراد المهام من ملفات خارجية
  bool canBulkEditTasks() => hasPermission('tasks.bulk_edit'); // تستخدم للتعديل المجمع للمهام - لم يتم العثور على استخدامات مباشرة حالياً
  bool canBulkDeleteTasks() => hasPermission('tasks.bulk_delete'); // تستخدم للحذف المجمع للمهام - لم يتم العثور على استخدامات مباشرة حالياً
  bool canViewGanttChart() => hasPermission('tasks.gantt_view'); // تستخدم لعرض مخطط جانت - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لعرض المهام في شكل مخطط زمني
  bool canViewTaskBoard() => hasPermission('tasks.board_view'); // تستخدم لعرض لوحة المهام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لعرض المهام في شكل كانبان
  bool canViewTimeline() => hasPermission('tasks.timeline_view'); // تستخدم لعرض الجدول الزمني للمهام - لم يتم العثور على استخدامات مباشرة حالياً

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canViewTaskDetails() => hasPermission('tasks.view_details');
  bool canUpdateTaskProgress() => hasPermission('tasks.update_progress');
  bool canFilterTasks() => hasPermission('tasks.filter');
  bool canSortTasks() => hasPermission('tasks.sort');
  bool canManageTaskBoard() => hasPermission('tasks.manage_board');
  bool canChangeTaskStatus() => hasPermission('tasks.change_status');
  bool canChangeTaskPriority() => hasPermission('tasks.change_priority');

  // الصلاحيات الجديدة المضافة من قاعدة البيانات
  bool canRefreshTasks() => hasPermission('tasks.refresh');
  bool canViewWorkloadReport() => hasPermission('tasks.view_workload_report');

  /// صلاحيات أنواع المهام
  bool canViewTaskTypes() => hasPermission('task_types.view');
  bool canCreateTaskTypes() => hasPermission('task_types.create');
  bool canEditTaskTypes() => hasPermission('task_types.edit');
  bool canDeleteTaskTypes() => hasPermission('task_types.delete');
  bool canManageTaskTypes() => hasPermission('task_types.manage') ||
                               hasPermission('system.manage') ||
                               (canCreateTaskTypes() && canEditTaskTypes() && canDeleteTaskTypes());

  /// صلاحيات المستخدمين
  bool canViewUsers() => hasPermission('users.view'); // تستخدم لعرض قائمة المستخدمين - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لعرض المستخدمين الأساسي
  bool canCreateUser() => hasPermission('users.create'); // تستخدم لإنشاء مستخدمين جدد: زر إضافة مستخدم في user_management_screen.dart، حوار إنشاء المستخدم، والتحقق من الصلاحية قبل عرض أزرار الإضافة
  bool canEditUser() => hasPermission('users.edit'); // تستخدم لتعديل بيانات المستخدمين: قائمة الخيارات في user_management_screen.dart، حوار تعديل المستخدم، والتحقق من الصلاحية قبل عرض خيارات التعديل
  bool canManageUserRoles() => hasPermission('users.manage_roles'); // تستخدم لإدارة أدوار المستخدمين - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لتعيين وتغيير أدوار المستخدمين
  bool canViewAllUsers() => hasPermission('users.view_all'); // تستخدم لعرض جميع المستخدمين: زر التحديث في user_management_screen.dart، والتحقق من الصلاحية لتحميل قائمة كاملة بالمستخدمين
  bool canManageUserPermissions() => hasPermission('users.manage_permissions'); // تستخدم لإدارة صلاحيات المستخدمين - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لتعديل صلاحيات المستخدمين الفردية

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canRefreshUsers() => hasPermission('users.refresh');

  /// صلاحيات التقارير
  bool canAccessReports() => hasPermission('reports.view'); // تستخدم للوصول لقسم التقارير: التحقق من الوصول في home_screen.dart، والتحقق من الصلاحية في unified_permission_middleware.dart
  bool canCreateReport() => hasPermission('reports.create'); // تستخدم لإنشاء تقارير جديدة - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لإنشاء التقارير المخصصة
  bool canEditReport() => hasPermission('reports.edit'); // تستخدم لتعديل التقارير الموجودة - لم يتم العثور على استخدامات مباشرة حالياً
  bool canDeleteReport() => hasPermission('reports.delete'); // تستخدم لحذف التقارير - لم يتم العثور على استخدامات مباشرة حالياً
  bool canExportReport() => hasPermission('reports.export'); // تستخدم لتصدير التقارير: زر التصدير في report_toolbar.dart، وتصدير التقارير بصيغ مختلفة
  bool canScheduleReport() => hasPermission('reports.schedule'); // تستخدم لجدولة التقارير: زر الجدولة في report_toolbar.dart، وإعداد التقارير الدورية
  bool canShareReport() => hasPermission('reports.share'); // تستخدم لمشاركة التقارير: زر المشاركة في report_toolbar.dart، ومشاركة التقارير مع المستخدمين
  bool canPrintReport() => hasPermission('reports.print'); // تستخدم لطباعة التقارير: زر الطباعة في report_toolbar.dart، وطباعة التقارير
  bool canAccessAdvancedReports() => hasPermission('reports.advanced'); // تستخدم للوصول للتقارير المتقدمة - لم يتم العثور على استخدامات مباشرة حالياً
  bool canCreateCustomReport() => hasPermission('reports.custom'); // تستخدم لإنشاء تقارير مخصصة - لم يتم العثور على استخدامات مباشرة حالياً
  bool canUseReportBuilder() => hasPermission('reports.builder'); // تستخدم لاستخدام منشئ التقارير - لم يتم العثور على استخدامات مباشرة حالياً

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canViewContributionsReport() => hasPermission('reports.contributions');
  bool canExportPdfReports() => hasPermission('reports.pdf');
  bool canViewWorkloadReports() => hasPermission('reports.workload');

  // الصلاحيات الجديدة المضافة من قاعدة البيانات
  bool canPdfExportReports() => hasPermission('reports.pdf_export');
  bool canRefreshReports() => hasPermission('reports.refresh');

  /// صلاحيات النظام
  bool canManageSystem() => hasPermission('system.manage'); // تستخدم لإدارة النظام: التحقق في admin_dashboard_new.dart لأزرار التصدير والاستيراد، وإدارة النظام العامة
  bool canBackupSystem() => hasPermission('system.backup'); // تستخدم لعمل نسخ احتياطية: التحقق في admin_debug_helper.dart، وإنشاء النسخ الاحتياطية للنظام
  bool canRestoreSystem() => hasPermission('system.restore'); // تستخدم لاستعادة النظام من النسخ الاحتياطية - لم يتم العثور على استخدامات مباشرة حالياً
  bool canManageDatabase() => hasPermission('database.manage'); // تستخدم لإدارة قاعدة البيانات - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لعمليات قاعدة البيانات المتقدمة
  bool canViewSystemLogs() => hasPermission('system.logs'); // تستخدم لعرض سجلات النظام - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لمراقبة النظام

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canRepairDatabase() => hasPermission('database.repair');
  bool canBackupDatabase() => hasPermission('database.backup');
  bool canRestoreDatabase() => hasPermission('database.restore');

  /// صلاحيات الملف الشخصي
  bool canViewProfile() => hasPermission('profile.view'); // تستخدم لعرض الملف الشخصي - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لعرض بيانات المستخدم الشخصية
  bool canEditProfile() => hasPermission('profile.edit'); // تستخدم لتعديل الملف الشخصي - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لتعديل بيانات المستخدم الشخصية

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canChangePassword() => hasPermission('profile.change_password'); // تستخدم لتغيير كلمة المرور - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لتغيير كلمة مرور المستخدم

  /// صلاحيات الإشعارات
  bool canViewNotifications() => hasPermission('notifications.view');
  bool canManageNotifications() => hasPermission('notifications.manage');
  bool canCreateNotifications() => hasPermission('notifications.create');
  bool canEditNotifications() => hasPermission('notifications.edit');
  bool canDeleteNotifications() => hasPermission('notifications.delete');
  bool canBroadcastNotifications() => hasPermission('notifications.broadcast');
  bool canScheduleNotifications() => hasPermission('notifications.schedule');
  bool canConfigureNotifications() => hasPermission('notifications.settings');
  bool canSendNotifications() => hasPermission('notifications.send');

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canRefreshNotifications() => hasPermission('notifications.refresh');

  /// صلاحيات التقويم
  bool canManageCalendar() => hasPermission('calendar.manage');
  bool canCreateCalendarEvents() => hasPermission('calendar.create_events');
  bool canEditCalendarEvents() => hasPermission('calendar.edit_events');
  bool canDeleteCalendarEvents() => hasPermission('calendar.delete_events');
  bool canShareCalendarEvents() => hasPermission('calendar.share_events');
  bool canInviteUsersToEvents() => hasPermission('calendar.invite_users');
  bool canSetReminders() => hasPermission('calendar.set_reminders');
  bool canViewAllCalendarEvents() => hasPermission('calendar.view_all');
  bool canExportCalendar() => hasPermission('calendar.export');

  /// صلاحيات الأقسام
  bool canViewDepartments() => hasPermission('departments.view'); // تستخدم لعرض الأقسام: التحقق من الوصول في departments_tab.dart، والتحقق من الصلاحية لعرض قسم المستخدم فقط
  bool canManageDepartments() => hasPermission('departments.manage'); // تستخدم لإدارة جميع الأقسام: التحقق في departments_tab.dart للمدير العام، وإمكانية الوصول لجميع الأقسام والتحكم الكامل بها
  bool canAdminDepartments() => hasPermission('departments.admin'); // تستخدم لإدارة قسم محدد: التحقق في departments_tab.dart لمدير القسم، تحميل مهام القسم في task_controller.dart، والوصول لقسم المستخدم والأقسام الفرعية فقط

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canAssignDepartmentManager() => hasPermission('departments.assign_manager');
  bool canAddUsersToDepartment() => hasPermission('departments.add_users');
  bool canRemoveUsersFromDepartment() => hasPermission('departments.remove_users');
  bool canManageDepartmentUsers() => hasPermission('departments.manage_users');

  /// التحقق من أن المستخدم مدير لقسم محدد
  bool isUserDepartmentManager(int departmentId) {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) return false;

      // التحقق من أن المستخدم لديه صلاحية إدارة الأقسام
      if (!canAdminDepartments()) return false;

      // التحقق من أن المستخدم في نفس القسم
      if (currentUser.departmentId != departmentId) return false;

      // TODO: يجب إضافة التحقق من أن المستخدم هو المدير الفعلي للقسم
      // حالياً نفترض أن أي مستخدم لديه canAdminDepartments في قسم معين هو المدير
      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من إدارة القسم: $e');
      return false;
    }
  }

  /// التحقق من إمكانية الوصول لقسم محدد (للمدير أو المدير العام)
  bool canAccessDepartment(int departmentId) {
    // المدير العام يمكنه الوصول لجميع الأقسام
    if (canManageDepartments()) return true;

    // مدير القسم يمكنه الوصول لقسمه والأقسام الفرعية
    if (canAdminDepartments()) {
      return canAccessDepartmentAsManager(departmentId);
    }

    return false;
  }

  /// التحقق من إمكانية وصول مدير القسم لقسم محدد (قسمه فقط)
  bool canAccessDepartmentAsManager(int departmentId) {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser?.departmentId == null) return false;

      final userDepartmentId = currentUser!.departmentId!;

      // مدير القسم يمكنه الوصول لقسمه فقط
      if (departmentId == userDepartmentId) {
        debugPrint('✅ مدير القسم يصل لقسمه الأساسي: $departmentId');
        return true;
      }

      // لا يمكن الوصول للأقسام الأخرى
      debugPrint('❌ مدير القسم $userDepartmentId لا يمكنه الوصول للقسم $departmentId');
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من وصول مدير القسم: $e');
      return false;
    }
  }

  /// الحصول على نوع صلاحية الأقسام للمستخدم الحالي
  String getDepartmentPermissionLevel() {
    if (canManageDepartments()) return 'مدير عام - جميع الأقسام';
    if (canAdminDepartments()) return 'مدير قسم - قسم محدد';
    if (canViewDepartments()) return 'عرض فقط - قسم المستخدم';
    return 'لا توجد صلاحيات';
  }

  /// التحقق من أن المستخدم يمكنه رؤية قسم معين
  bool canViewSpecificDepartment(int departmentId) {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) return false;

      // المدير العام يرى جميع الأقسام
      if (canManageDepartments()) return true;

      // مدير القسم يرى قسمه والأقسام الفرعية
      if (canAdminDepartments()) {
        return canAccessDepartmentAsManager(departmentId);
      }

      // المستخدم العادي يرى قسمه فقط
      if (canViewDepartments() && currentUser.departmentId == departmentId) return true;

      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من رؤية القسم: $e');
      return false;
    }
  }

  // ========================================
  // 🎯 صلاحيات المحادثات والتواصل
  // ========================================

  /// صلاحيات المحادثات
  bool canAccessChat() => hasPermission('chat.view'); // تستخدم للوصول للمحادثات: التحقق من الوصول في home_screen.dart، والوصول لقسم المحادثات والرسائل
  bool canSendMessages() => hasPermission('chat.send'); // تستخدم لإرسال الرسائل - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لإرسال رسائل في المحادثات
  bool canDeleteMessages() => hasPermission('chat.delete_messages'); // تستخدم لحذف الرسائل - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة لحذف الرسائل من المحادثات
  bool canSearchInChat() => hasPermission('chat.search'); // تستخدم للبحث في المحادثات - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة للبحث في محتوى المحادثات
  bool canCreateChatGroup() => hasPermission('chat.create_group');
  bool canEditChatGroup() => hasPermission('chat.edit_group');
  bool canDeleteChatGroup() => hasPermission('chat.delete_group');
  bool canAddChatMembers() => hasPermission('chat.add_members');
  bool canRemoveChatMembers() => hasPermission('chat.remove_members');
  bool canMuteChat() => hasPermission('chat.mute');
  bool canUnmuteChat() => hasPermission('chat.unmute');
  bool canPinMessages() => hasPermission('chat.pin_messages');
  bool canUnpinMessages() => hasPermission('chat.unpin_messages');
  bool canForwardMessages() => hasPermission('chat.forward');
  bool canReplyToMessages() => hasPermission('chat.reply');
  bool canEditMessages() => hasPermission('chat.edit_messages');
  bool canReactToMessages() => hasPermission('chat.react');

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canMarkMessageForFollowup() => hasPermission('messages.mark_followup');
  bool canPinMessage() => hasPermission('messages.pin');
  bool canEditMessage() => hasPermission('messages.edit');
  bool canDeleteMessage() => hasPermission('messages.delete');
  bool canReplyToMessage() => hasPermission('messages.reply');

  // الصلاحيات الجديدة المضافة من قاعدة البيانات
  bool canRefreshChat() => hasPermission('chat.refresh');

  // ========================================
  // 🎯 صلاحيات الأرشيف والمستندات
  // ========================================

  /// صلاحيات الأرشيف
  bool canAccessArchive() => hasPermission('archive.view');
  bool canUploadToArchive() => hasPermission('archive.upload');
  bool canDownloadFromArchive() => hasPermission('archive.download');
  bool canDeleteFromArchive() => hasPermission('archive.delete');
  bool canManageArchiveCategories() => hasPermission('archive.manage_categories');

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canAdvancedArchiveSearch() => hasPermission('archive.advanced_search');

  /// صلاحيات البحث
  bool canAccessSearch() => hasPermission('search.view');
  bool canUseAdvancedSearch() => hasPermission('search.advanced');
  bool canExportSearchResults() => hasPermission('search.export');

  /// صلاحيات المرفقات الخاصه بالمهام (tasks_attachments)
  bool canViewAttachments() => hasPermission('attachments.view');
  bool canUploadAttachments() => hasPermission('attachments.upload');
  bool canDownloadAttachments() => hasPermission('attachments.download');
  bool canDeleteAttachments() => hasPermission('attachments.delete');
  bool canShareAttachments() => hasPermission('attachments.share');
  bool canManageAttachments() => hasPermission('attachments.manage');

  /// صلاحيات الملفات
  bool canViewFiles() => hasPermission('files.view');
  bool canUploadFiles() => hasPermission('files.upload');
  bool canDownloadFiles() => hasPermission('files.download');
  bool canDeleteFiles() => hasPermission('files.delete');
  bool canShareFiles() => hasPermission('files.share');
  bool canPreviewFiles() => hasPermission('files.preview');
  bool canEditFiles() => hasPermission('files.edit');

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canUploadMultipleFiles() => hasPermission('files.upload_multiple');
  bool canBulkFileOperations() => hasPermission('files.bulk_operations');

  /// صلاحيات المستندات النصية
  bool canViewDocuments() => hasPermission('documents.view');
  bool canCreateDocuments() => hasPermission('documents.create');
  bool canEditDocuments() => hasPermission('documents.edit');
  bool canDeleteDocuments() => hasPermission('documents.delete');
  bool canShareDocuments() => hasPermission('documents.share');
  bool canExportDocuments() => hasPermission('documents.export');
  bool canViewDocumentHistory() => hasPermission('documents.version_history');

  // الصلاحيات المفقودة - المضافة من قاعدة البيانات
  bool canCreateDocumentFromTemplate() => hasPermission('documents.create_from_template');

  /// صلاحيات التعليقات
  bool canViewComments() => hasPermission('comments.view');
  bool canCreateComments() => hasPermission('comments.create');
  bool canEditComments() => hasPermission('comments.edit');
  bool canDeleteComments() => hasPermission('comments.delete');
  bool canReplyToComments() => hasPermission('comments.reply');
  bool canModerateComments() => hasPermission('comments.moderate');
  bool canManageComments() => hasPermission('comments.manage');

  // ========================================
  // 🎯 صلاحيات Power BI والتحليلات
  // ========================================

  /// صلاحيات Power BI
  bool canAccessPowerBI() => hasPermission('powerbi.view');
  bool canCreatePowerBIReports() => hasPermission('powerbi.create');
  bool canEditPowerBIReports() => hasPermission('powerbi.edit');
  bool canDeletePowerBIReports() => hasPermission('powerbi.delete');
  bool canSharePowerBIReports() => hasPermission('powerbi.share');
  bool canEmbedPowerBIReports() => hasPermission('powerbi.embed');
  bool canRefreshPowerBIData() => hasPermission('powerbi.refresh');

  // ========================================
  // 🎯 صلاحيات الإعدادات والتكوين
  // ========================================

  /// صلاحيات الإعدادات
  bool canViewSettings() => hasPermission('settings.view');
  bool canEditSettings() => hasPermission('settings.edit');
  bool canManageSettings() => hasPermission('settings.manage');
  bool canAccessSystemSettings() => hasPermission('settings.system');
  bool canAccessUserSettings() => hasPermission('settings.user');
  bool canChangeTheme() => hasPermission('settings.theme');
  bool canChangeLanguage() => hasPermission('settings.language');
  bool canConfigureSync() => hasPermission('settings.sync');
  bool canManagePrivacy() => hasPermission('settings.privacy');

  // الصلاحيات المفقودة - المضافة حديثاً
  bool canTestPermissions() => hasPermission('admin.test_permissions');
  bool canAccessDebugTools() => hasPermission('admin.debug');
  bool canConfigureNotificationSettings() => hasPermission('settings.notifications');
  bool canAccessDynamicReports() => hasPermission('reports.dynamic_access');
  bool canAccessDatabaseRepair() => hasPermission('admin.database_repair');
  bool canViewArchiveDocuments() => hasPermission('archive.view_documents');
  bool canManageSearchHistory() => hasPermission('search.manage_history');

  // الصلاحيات الجديدة المضافة من قاعدة البيانات
  bool canSystemDiagnostics() => hasPermission('admin.system_diagnostics');
  bool canRefreshAdminData() => hasPermission('admin.refresh_data');
  bool canAccessEnhancedAdmin() => hasPermission('system.enhanced_admin');
  bool canChangeSystemTheme() => hasPermission('system.theme_change');
  bool canAccessHelpSystem() => hasPermission('system.help_access');
  bool canRefreshRoles() => hasPermission('roles.refresh');
  bool canRefreshPermissions() => hasPermission('permissions.refresh');

  // ========================================
  // 🎯 صلاحيات البيانات والأمان
  // ========================================

  /// صلاحيات إدارة البيانات
  bool canExportData() => hasPermission('data.export');
  bool canImportData() => hasPermission('data.import');
  bool canBackupData() => hasPermission('data.backup');
  bool canRestoreData() => hasPermission('data.restore');
  bool canMigrateData() => hasPermission('data.migrate');
  bool canSyncData() => hasPermission('data.sync');

  /// صلاحيات الأمان
  bool canViewSecurityLogs() => hasPermission('security.view_logs');
  bool canAuditSecurity() => hasPermission('security.audit');
  bool canManageSessions() => hasPermission('security.manage_sessions');
  bool canUseTwoFactor() => hasPermission('security.two_factor');
  bool canManagePasswordPolicy() => hasPermission('security.password_policy');

  /// صلاحيات سجلات النشاط
  bool canViewActivity() => hasPermission('activity.view');
  bool canExportActivity() => hasPermission('activity.export');
  bool canFilterActivity() => hasPermission('activity.filter');
  bool canViewActivities() => hasPermission('activities.view');

  // ========================================
  // 🎯 صلاحيات الطباعة والمشاركة
  // ========================================

  /// صلاحيات الطباعة
  bool canPrintDocuments() => hasPermission('print.documents');
  bool canPrintReports() => hasPermission('print.reports');
  bool canPrintTasks() => hasPermission('print.tasks');
  bool canPrintCalendar() => hasPermission('print.calendar');

  /// صلاحيات المشاركة
  bool canShareDocumentsExternal() => hasPermission('share.documents');
  bool canShareReportsExternal() => hasPermission('share.reports');
  bool canShareTasksExternal() => hasPermission('share.tasks');
  bool canShareCalendarExternal() => hasPermission('share.calendar');
  bool canShareExternal() => hasPermission('share.external');

  // ========================================
  // 🎯 صلاحيات الإحصائيات والتحليلات
  // ========================================

  /// صلاحيات الإحصائيات
  bool canViewStatistics() => hasPermission('statistics.view');
  bool canViewAdvancedStatistics() => hasPermission('statistics.advanced');

  /// صلاحيات الوسوم
  bool canViewTags() => hasPermission('tags.view');
  bool canManageTags() => hasPermission('tags.manage');

  /// صلاحيات التكاملات
  bool canViewIntegrations() => hasPermission('integrations.view');
  bool canManageIntegrations() => hasPermission('integrations.manage');

  // ========================================
  // 🎯 صلاحيات الإدارة والدعم
  // ========================================

  /// صلاحيات الإدارة
  bool canAccessAdmin() => hasPermission('admin.view'); // تستخدم للوصول للوحة الإدارة: التحقق في admin_dashboard_new.dart، unified_permission_middleware.dart، وأدوات التشخيص admin_debug_helper.dart

  /// صلاحيات الاختبار والتطوير
  bool canAccessTesting() => hasPermission('testing.access'); // تستخدم للوصول لأدوات الاختبار والتشخيص: التحقق في admin_debug_helper.dart، permission_debug_helper.dart، وحماية أزرار التشخيص من الوصول غير المصرح
  bool canAccessAPI() => hasPermission('api.access'); // تستخدم للوصول لواجهة برمجة التطبيقات - لم يتم العثور على استخدامات مباشرة حالياً، مخصصة للوصول المباشر للـ API

  /// صلاحيات المساعدة والدعم
  bool canViewHelp() => hasPermission('help.view');
  bool canContactSupport() => hasPermission('support.contact');

  // ========================================
  // 🎯 صلاحيات الموارد البشرية
  // ========================================

  /// صلاحيات الموارد البشرية
  bool canAccessHR() => hasPermission('hr.view'); // الوصول العام لوحدة الموارد البشرية

  // صلاحيات إدارة الموظفين
  bool canManageEmployees() => hasPermission('hr.manage_employees'); // إدارة الموظفين - إضافة وتعديل وحذف
  bool canViewEmployees() => hasPermission('hr.view_employees'); // عرض قائمة الموظفين
  bool canAddEmployee() => hasPermission('hr.add_employee'); // إضافة موظف جديد
  bool canEditEmployee() => hasPermission('hr.edit_employee'); // تعديل بيانات الموظف
  bool canDeleteEmployee() => hasPermission('hr.delete_employee'); // حذف موظف

  // صلاحيات الحضور والانصراف
  bool canViewAttendance() => hasPermission('hr.view_attendance'); // عرض الحضور والانصراف
  bool canManageAttendance() => hasPermission('hr.manage_attendance'); // إدارة الحضور والانصراف
  bool canMarkAttendance() => hasPermission('hr.mark_attendance'); // تسجيل الحضور والانصراف
  bool canEditAttendance() => hasPermission('hr.edit_attendance'); // تعديل سجلات الحضور

  // صلاحيات الرواتب
  bool canViewPayroll() => hasPermission('hr.view_payroll'); // عرض الرواتب
  bool canManagePayroll() => hasPermission('hr.manage_payroll'); // إدارة الرواتب
  bool canCreatePayroll() => hasPermission('hr.create_payroll'); // إنشاء كشف راتب
  bool canApprovePayroll() => hasPermission('hr.approve_payroll'); // اعتماد الرواتب

  // صلاحيات الإجازات
  bool canViewLeaves() => hasPermission('hr.view_leaves'); // عرض الإجازات
  bool canManageLeaves() => hasPermission('hr.manage_leaves'); // إدارة الإجازات
  bool canRequestLeave() => hasPermission('hr.request_leave'); // طلب إجازة
  bool canApproveLeave() => hasPermission('hr.approve_leave'); // اعتماد الإجازات

  // صلاحيات تقييم الأداء
  bool canViewPerformance() => hasPermission('hr.view_performance'); // عرض تقييم الأداء
  bool canManagePerformance() => hasPermission('hr.manage_performance'); // إدارة تقييم الأداء
  bool canCreatePerformanceReview() => hasPermission('hr.create_performance_review'); // إنشاء تقييم أداء

  // صلاحيات التدريب
  bool canViewTraining() => hasPermission('hr.view_training'); // عرض التدريب
  bool canManageTraining() => hasPermission('hr.manage_training'); // إدارة التدريب
  bool canCreateTrainingProgram() => hasPermission('hr.create_training_program'); // إنشاء برنامج تدريبي

  // صلاحيات السلف والمساعدات المالية
  bool canViewAdvances() => hasPermission('hr.view_advances'); // عرض السلف والمساعدات المالية
  bool canManageAdvances() => hasPermission('hr.manage_advances'); // إدارة السلف والمساعدات المالية
  bool canCreateAdvance() => hasPermission('hr.create_advance'); // إنشاء طلب سلفة جديدة
  bool canApproveAdvance() => hasPermission('hr.approve_advance'); // اعتماد السلف
  bool canRejectAdvance() => hasPermission('hr.reject_advance'); // رفض السلف
  bool canRecordAdvancePayment() => hasPermission('hr.record_advance_payment'); // تسجيل دفعات السلف
  bool canDeleteAdvance() => hasPermission('hr.delete_advance'); // حذف السلف

  // صلاحيات التقارير
  bool canViewHRReports() => hasPermission('hr.view_reports'); // عرض تقارير الموارد البشرية
  bool canCreateHRReports() => hasPermission('hr.create_reports'); // إنشاء تقارير الموارد البشرية
  bool canExportHRReports() => hasPermission('hr.export_reports'); // تصدير تقارير الموارد البشرية

  // صلاحيات إدارية متقدمة
  bool canAdminHR() => hasPermission('hr.admin'); // إدارة شاملة للموارد البشرية
  bool canHRSettings() => hasPermission('hr.settings'); // إعدادات الموارد البشرية

  /// صلاحيات إدارة الصلاحيات - تعتمد فقط على الصلاحيات الفعلية
  bool canManagePermissions() => hasPermission('permissions.manage') || hasPermission('users.manage_roles') || hasPermission('system.manage');

  // ========================================
  // 🎯 صلاحيات مركبة ومتقدمة
  // ========================================

  /// صلاحيات مركبة للمستخدمين - تعتمد فقط على الصلاحيات الفعلية
  bool canDeleteUser() => hasPermission('users.delete');
  bool canViewReports() => hasPermission('reports.view');
  bool canUploadDocument() => hasPermission('archive.upload') || hasPermission('files.upload');
  bool canDownloadDocument() => hasPermission('archive.download') || hasPermission('files.download');
  bool canDeleteDocument() => hasPermission('archive.delete') || hasPermission('files.delete');
  bool canManageCategories() => hasPermission('archive.manage_categories');

  /// منح صلاحية للمستخدم (عبر الدور)
  Future<bool> grantPermission(int userId, int permissionId) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser != null) {
        // منح الصلاحية للدور
        await _apiService.grantPermissionToRole(currentUser.role.value, permissionId);

        // تحديث الصلاحيات محلياً
        final permission = _permissions.firstWhereOrNull((p) => p.id == permissionId);
        if (permission != null) {
          _userPermissions[permission.name] = true;
        }

        debugPrint('تم منح الصلاحية للمستخدم');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية: $e');
      return false;
    }
  }

  /// إلغاء صلاحية من المستخدم (عبر الدور)
  Future<bool> revokePermission(int userId, int permissionId) async {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser != null) {
        // إلغاء الصلاحية من الدور
        await _apiService.revokePermissionFromRole(currentUser.role.value, permissionId);

        // تحديث الصلاحيات محلياً
        final permission = _permissions.firstWhereOrNull((p) => p.id == permissionId);
        if (permission != null) {
          _userPermissions[permission.name] = false;
        }

        debugPrint('تم إلغاء الصلاحية من المستخدم');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية: $e');
      return false;
    }
  }

  /// منح صلاحية ديناميكية لمستخدم (حسب scope/type)
  Future<bool> grantPermissionToUser({
    required int userId,
    required String scope,
    required String type,
    String? description,
  }) async {
    try {
      // TODO: تنفيذ منح الصلاحية للمستخدم عند توفر الـ API
      debugPrint('تم منح الصلاحية للمستخدم $userId: $type - $scope');
      return true;
    } catch (e) {
      debugPrint('خطأ في منح الصلاحية للمستخدم: $e');
      return false;
    }
  }

  /// إلغاء صلاحية ديناميكية من مستخدم (حسب scope/type)
  Future<bool> revokePermissionFromUser({
    required int userId,
    required String scope,
    required String type,
    String? description,
  })
   async {
    try {
      // TODO: تنفيذ إلغاء الصلاحية من المستخدم عند توفر الـ API
      debugPrint('تم إلغاء الصلاحية من المستخدم $userId: $type - $scope');
      return true;
    } catch (e) {
      debugPrint('خطأ في إلغاء الصلاحية من المستخدم: $e');
      return false;
    }
  }

  /// تحديث صلاحيات المستخدم الحالي
  Future<void> refreshCurrentUserPermissions() async {
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;
    
    if (currentUser != null) {
      await loadUserPermissions(currentUser.id);
    }
  }

  /// مسح الصلاحيات المحلية
  void clearPermissions() {
    _userPermissions.clear();
  }

  /// التحقق من صلاحية المستخدم عبر API المحسن
  Future<bool> checkPermissionAsync(String permissionName) async {
    try {
      debugPrint('🔍 التحقق من صلاحية الوصول إلى: $permissionName');
      
      // الحصول على معرف المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // استخدام API للتحقق من الصلاحية
      final hasPermission = await _apiService.checkUserPermission(currentUser.id, permissionName);
      
      // تحديث التخزين المحلي
      _userPermissions[permissionName] = hasPermission;
      
      debugPrint('✅ نتيجة التحقق من صلاحية $permissionName: $hasPermission');
      return hasPermission;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحية $permissionName: $e');
      return false;
    }
  }

  /// جلب جميع صلاحيات المستخدم الحالي من الخادم - يعتمد فقط على الصلاحيات المخصصة
  Future<void> loadAllUserPermissions() async {
    try {
      debugPrint('🔄 جلب جميع صلاحيات المستخدم من الخادم...');

      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return;
      }

      // جلب الصلاحيات المخصصة فقط - لا نحمل صلاحيات الدور الافتراضي
      final allPermissions = await _apiService.getAllUserPermissions(currentUser.id);

      // مسح الصلاحيات القديمة وإضافة الجديدة
      _userPermissions.clear();
      for (final permission in allPermissions) {
        _userPermissions[permission] = true;
      }

      debugPrint('✅ تم تحميل ${allPermissions.length} صلاحية مخصصة للمستخدم ${currentUser.name}');
      debugPrint('📋 الصلاحيات المحملة: ${allPermissions.join(', ')}');
    } catch (e) {
      debugPrint('❌ خطأ في جلب صلاحيات المستخدم: $e');
    }
  }

  /// التحقق من صلاحيات متعددة دفعة واحدة
  Future<Map<String, bool>> checkMultiplePermissions(List<String> permissionNames) async {
    try {
      debugPrint('🔍 التحقق من ${permissionNames.length} صلاحية دفعة واحدة');
      
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      
      if (currentUser == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return Map.fromIterable(permissionNames, value: (_) => false);
      }

      // استخدام API للتحقق من صلاحيات متعددة
      final results = await _apiService.checkMultiplePermissions(currentUser.id, permissionNames);
      
      // تحديث التخزين المحلي
      for (final entry in results.entries) {
        _userPermissions[entry.key] = entry.value;
      }
      
      debugPrint('✅ تم التحقق من ${results.length} صلاحية');
      return results;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الصلاحيات المتعددة: $e');
      return Map.fromIterable(permissionNames, value: (_) => false);
    }
  }

  /// التحقق من صلاحية الوصول إلى واجهة معينة
  Future<bool> checkInterfaceAccess(String interfaceName) async {
    try {
      // خريطة ربط أسماء الواجهات بالصلاحيات - محدثة لتطابق قاعدة البيانات
      final interfacePermissions = {
        // الواجهات الرئيسية
        'dashboard': 'dashboard.admin',
        'tasks': 'tasks.view',
        'users': 'users.view',
        'reports': 'reports.view',
        'calendar': 'calendar.manage',
        'departments': 'departments.view',

        // التواصل والمحادثات
        'messages': 'chat.view',
        'chat': 'chat.view',
        'notifications': 'notifications.view',

        // الملفات والمستندات
        'archive': 'archive.view',
        'files': 'files.view',
        'documents': 'documents.view',
        'attachments': 'attachments.view',
        'search': 'search.view',

        // التحليلات والتقارير
        'power_bi': 'powerbi.view',
        'powerbi': 'powerbi.view',
        'statistics': 'statistics.view',
        'analytics': 'statistics.view',

        // الإدارة والنظام
        'admin': 'admin.view',
        'system': 'system.manage',
        'settings': 'settings.view',
        'security': 'security.view_logs',
        'permissions': 'permissions.manage',

        // الملف الشخصي والمساعدة
        'profile': 'profile.view',
        'help': 'help.view',
        'support': 'support.contact',

        // أخرى
        'comments': 'comments.view',
        'tags': 'tags.view',
        'integrations': 'integrations.view',
        'testing': 'testing.access',
        'api': 'api.access',

        // للتوافق مع الإصدارات السابقة
        'reports2': 'reports.view',
      };

      final permissionName = interfacePermissions[interfaceName];
      if (permissionName != null) {
        // التحقق من الصلاحية عبر API
        return await checkPermissionAsync(permissionName);
      }

      // إذا لم تكن الواجهة معرفة، ارجع false
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية الوصول للواجهة $interfaceName: $e');
      return false;
    }
  }
}
