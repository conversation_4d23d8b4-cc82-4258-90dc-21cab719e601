/// نموذج الموظف الشامل متوافق مع Backend API
class Employee {
  final int id;
  final String name;
  final String email;
  final String? username;
  final String? firstName;
  final String? lastName;
  final String? profileImage;
  final bool isActive;
  final bool isOnline;
  final int? departmentId;
  final String? departmentName;
  final int? roleId;
  final String? roleName;

  // البيانات الشخصية للموارد البشرية
  final String? fullNameArabic;
  final DateTime? birthDate;
  final String? birthPlace;
  final String? bloodType;
  final String? qualification;

  // بيانات السكن
  final String? currentAddress;
  final String? governorate;
  final String? directorate;
  final String? isolation;
  final String? village;

  // بيانات الوظيفة
  final String? employeeId;
  final DateTime? hireDate;
  final String? jobTitle;
  final String? employmentStatus;
  final String? salaryGrade;

  // بيانات إضافية
  final String? skills;
  final String? hobbies;
  final String? employeeNotes;

  // تواريخ النظام
  final int createdAt;
  final int? employeeCreatedAt;
  final int? employeeUpdatedAt;
  final int? lastLogin;
  final int? lastSeen;

  // حقول الأمان
  final bool twoFactorEnabled;

  const Employee({
    required this.id,
    required this.name,
    required this.email,
    this.username,
    this.firstName,
    this.lastName,
    this.profileImage,
    this.isActive = true,
    this.isOnline = false,
    this.departmentId,
    this.departmentName,
    this.roleId,
    this.roleName,
    this.fullNameArabic,
    this.birthDate,
    this.birthPlace,
    this.bloodType,
    this.qualification,
    this.currentAddress,
    this.governorate,
    this.directorate,
    this.isolation,
    this.village,
    this.employeeId,
    this.hireDate,
    this.jobTitle,
    this.employmentStatus,
    this.salaryGrade,
    this.skills,
    this.hobbies,
    this.employeeNotes,
    required this.createdAt,
    this.employeeCreatedAt,
    this.employeeUpdatedAt,
    this.lastLogin,
    this.lastSeen,
    this.twoFactorEnabled = false,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      username: json['username'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      profileImage: json['profileImage'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      isOnline: json['isOnline'] as bool? ?? false,
      departmentId: json['departmentId'] as int?,
      departmentName: json['departmentName'] as String?,
      roleId: json['roleId'] as int?,
      roleName: json['roleName'] as String?,
      fullNameArabic: json['fullNameArabic'] as String?,
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate'] as String) : null,
      birthPlace: json['birthPlace'] as String?,
      bloodType: json['bloodType'] as String?,
      qualification: json['qualification'] as String?,
      currentAddress: json['currentAddress'] as String?,
      governorate: json['governorate'] as String?,
      directorate: json['directorate'] as String?,
      isolation: json['isolation'] as String?,
      village: json['village'] as String?,
      employeeId: json['employeeId'] as String?,
      hireDate: json['hireDate'] != null ? DateTime.parse(json['hireDate'] as String) : null,
      jobTitle: json['jobTitle'] as String?,
      employmentStatus: json['employmentStatus'] as String?,
      salaryGrade: json['salaryGrade'] as String?,
      skills: json['skills'] as String?,
      hobbies: json['hobbies'] as String?,
      employeeNotes: json['employeeNotes'] as String?,
      createdAt: json['createdAt'] as int,
      employeeCreatedAt: json['employeeCreatedAt'] as int?,
      employeeUpdatedAt: json['employeeUpdatedAt'] as int?,
      lastLogin: json['lastLogin'] as int?,
      lastSeen: json['lastSeen'] as int?,
      twoFactorEnabled: json['twoFactorEnabled'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'username': username,
      'firstName': firstName,
      'lastName': lastName,
      'profileImage': profileImage,
      'isActive': isActive,
      'isOnline': isOnline,
      'departmentId': departmentId,
      'departmentName': departmentName,
      'roleId': roleId,
      'roleName': roleName,
      'fullNameArabic': fullNameArabic,
      'birthDate': birthDate?.toIso8601String(),
      'birthPlace': birthPlace,
      'bloodType': bloodType,
      'qualification': qualification,
      'currentAddress': currentAddress,
      'governorate': governorate,
      'directorate': directorate,
      'isolation': isolation,
      'village': village,
      'employeeId': employeeId,
      'hireDate': hireDate?.toIso8601String(),
      'jobTitle': jobTitle,
      'employmentStatus': employmentStatus,
      'salaryGrade': salaryGrade,
      'skills': skills,
      'hobbies': hobbies,
      'employeeNotes': employeeNotes,
      'createdAt': createdAt,
      'employeeCreatedAt': employeeCreatedAt,
      'employeeUpdatedAt': employeeUpdatedAt,
      'lastLogin': lastLogin,
      'lastSeen': lastSeen,
      'twoFactorEnabled': twoFactorEnabled,
    };
  }

  Employee copyWith({
    int? id,
    String? name,
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    String? profileImage,
    bool? isActive,
    bool? isOnline,
    int? departmentId,
    String? departmentName,
    int? roleId,
    String? roleName,
    String? fullNameArabic,
    DateTime? birthDate,
    String? birthPlace,
    String? bloodType,
    String? qualification,
    String? currentAddress,
    String? governorate,
    String? directorate,
    String? isolation,
    String? village,
    String? employeeId,
    DateTime? hireDate,
    String? jobTitle,
    String? employmentStatus,
    String? salaryGrade,
    String? skills,
    String? hobbies,
    String? employeeNotes,
    int? createdAt,
    int? employeeCreatedAt,
    int? employeeUpdatedAt,
    int? lastLogin,
    int? lastSeen,
    bool? twoFactorEnabled,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileImage: profileImage ?? this.profileImage,
      isActive: isActive ?? this.isActive,
      isOnline: isOnline ?? this.isOnline,
      departmentId: departmentId ?? this.departmentId,
      departmentName: departmentName ?? this.departmentName,
      roleId: roleId ?? this.roleId,
      roleName: roleName ?? this.roleName,
      fullNameArabic: fullNameArabic ?? this.fullNameArabic,
      birthDate: birthDate ?? this.birthDate,
      birthPlace: birthPlace ?? this.birthPlace,
      bloodType: bloodType ?? this.bloodType,
      qualification: qualification ?? this.qualification,
      currentAddress: currentAddress ?? this.currentAddress,
      governorate: governorate ?? this.governorate,
      directorate: directorate ?? this.directorate,
      isolation: isolation ?? this.isolation,
      village: village ?? this.village,
      employeeId: employeeId ?? this.employeeId,
      hireDate: hireDate ?? this.hireDate,
      jobTitle: jobTitle ?? this.jobTitle,
      employmentStatus: employmentStatus ?? this.employmentStatus,
      salaryGrade: salaryGrade ?? this.salaryGrade,
      skills: skills ?? this.skills,
      hobbies: hobbies ?? this.hobbies,
      employeeNotes: employeeNotes ?? this.employeeNotes,
      createdAt: createdAt ?? this.createdAt,
      employeeCreatedAt: employeeCreatedAt ?? this.employeeCreatedAt,
      employeeUpdatedAt: employeeUpdatedAt ?? this.employeeUpdatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      lastSeen: lastSeen ?? this.lastSeen,
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
    );
  }

  /// الحصول على الاسم المفضل للعرض
  String get displayName => fullNameArabic?.isNotEmpty == true ? fullNameArabic! : name;

  /// الراتب الأساسي كرقم (تحويل من salaryGrade)
  double get basicSalary {
    if (salaryGrade == null || salaryGrade!.isEmpty) {
      return 0.0;
    }
    return double.tryParse(salaryGrade!) ?? 0.0;
  }

  /// الحصول على العمر
  int? get age {
    if (birthDate == null) return null;
    final now = DateTime.now();
    int age = now.year - birthDate!.year;
    if (now.month < birthDate!.month || 
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      age--;
    }
    return age;
  }

  /// الحصول على سنوات الخبرة
  int? get yearsOfExperience {
    if (hireDate == null) return null;
    final now = DateTime.now();
    int years = now.year - hireDate!.year;
    if (now.month < hireDate!.month || 
        (now.month == hireDate!.month && now.day < hireDate!.day)) {
      years--;
    }
    return years;
  }

  /// التحقق من كون الموظف نشط
  bool get isEmployeeActive => employmentStatus == 'active' && isActive;

  /// الحصول على حالة التوظيف بالعربية
  String get employmentStatusArabic {
    switch (employmentStatus) {
      case 'active':
        return 'نشط';
      case 'inactive':
        return 'غير نشط';
      case 'terminated':
        return 'منتهي الخدمة';
      default:
        return 'غير محدد';
    }
  }

  @override
  String toString() {
    return 'Employee(id: $id, name: $displayName, employeeId: $employeeId, status: $employmentStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Employee && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// DTO لإنشاء موظف جديد
class CreateEmployeeDto {
  final String name;
  final String email;
  final String? username;
  final String password;
  final String? firstName;
  final String? lastName;
  final int? departmentId;
  final int? roleId;
  final String? fullNameArabic;
  final DateTime? birthDate;
  final String? birthPlace;
  final String? bloodType;
  final String? qualification;
  final String? currentAddress;
  final String? governorate;
  final String? directorate;
  final String? isolation;
  final String? village;
  final String? employeeId; // اختياري - سيتم توليده في Backend
  final DateTime? hireDate;
  final String? jobTitle;
  final String? employmentStatus;
  final String? salaryGrade;
  final String? skills;
  final String? hobbies;
  final String? employeeNotes;

  const CreateEmployeeDto({
    required this.name,
    required this.email,
    this.username,
    required this.password,
    this.firstName,
    this.lastName,
    this.departmentId,
    this.roleId,
    this.fullNameArabic,
    this.birthDate,
    this.birthPlace,
    this.bloodType,
    this.qualification,
    this.currentAddress,
    this.governorate,
    this.directorate,
    this.isolation,
    this.village,
    required this.employeeId,
    this.hireDate,
    this.jobTitle,
    this.employmentStatus = 'active',
    this.salaryGrade,
    this.skills,
    this.hobbies,
    this.employeeNotes,
  });

  factory CreateEmployeeDto.fromJson(Map<String, dynamic> json) {
    return CreateEmployeeDto(
      name: json['name'] as String,
      email: json['email'] as String,
      username: json['username'] as String?,
      password: json['password'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      departmentId: json['departmentId'] as int?,
      roleId: json['roleId'] as int?,
      fullNameArabic: json['fullNameArabic'] as String?,
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate'] as String) : null,
      birthPlace: json['birthPlace'] as String?,
      bloodType: json['bloodType'] as String?,
      qualification: json['qualification'] as String?,
      currentAddress: json['currentAddress'] as String?,
      governorate: json['governorate'] as String?,
      directorate: json['directorate'] as String?,
      isolation: json['isolation'] as String?,
      village: json['village'] as String?,
      employeeId: json['employeeId'] as String? ?? '',
      hireDate: json['hireDate'] != null ? DateTime.parse(json['hireDate'] as String) : null,
      jobTitle: json['jobTitle'] as String?,
      employmentStatus: json['employmentStatus'] as String?,
      salaryGrade: json['salaryGrade'] as String?,
      skills: json['skills'] as String?,
      hobbies: json['hobbies'] as String?,
      employeeNotes: json['employeeNotes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'Email': email,
      'Username': username,
      'Password': password,
      'FirstName': firstName,
      'LastName': lastName,
      'DepartmentId': departmentId,
      'RoleId': roleId,
      'FullNameArabic': fullNameArabic,
      'BirthDate': birthDate?.toIso8601String(),
      'BirthPlace': birthPlace,
      'BloodType': bloodType,
      'Qualification': qualification,
      'CurrentAddress': currentAddress,
      'Governorate': governorate,
      'Directorate': directorate,
      'Isolation': isolation,
      'Village': village,
      'EmployeeId': employeeId,
      'HireDate': hireDate?.toIso8601String(),
      'JobTitle': jobTitle,
      'EmploymentStatus': employmentStatus,
      'SalaryGrade': salaryGrade,
      'Skills': skills,
      'Hobbies': hobbies,
      'EmployeeNotes': employeeNotes,
    };
  }
}

/// DTO لتحديث بيانات الموظف
class UpdateEmployeeHRInfoDto {
  final String? fullNameArabic;
  final DateTime? birthDate;
  final String? birthPlace;
  final String? bloodType;
  final String? qualification;
  final String? currentAddress;
  final String? governorate;
  final String? directorate;
  final String? isolation;
  final String? village;
  final String? employeeId;
  final DateTime? hireDate;
  final String? jobTitle;
  final String? employmentStatus;
  final String? salaryGrade;
  final String? skills;
  final String? hobbies;
  final String? employeeNotes;

  const UpdateEmployeeHRInfoDto({
    this.fullNameArabic,
    this.birthDate,
    this.birthPlace,
    this.bloodType,
    this.qualification,
    this.currentAddress,
    this.governorate,
    this.directorate,
    this.isolation,
    this.village,
    this.employeeId,
    this.hireDate,
    this.jobTitle,
    this.employmentStatus,
    this.salaryGrade,
    this.skills,
    this.hobbies,
    this.employeeNotes,
  });

  factory UpdateEmployeeHRInfoDto.fromJson(Map<String, dynamic> json) {
    return UpdateEmployeeHRInfoDto(
      fullNameArabic: json['fullNameArabic'] as String?,
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate'] as String) : null,
      birthPlace: json['birthPlace'] as String?,
      bloodType: json['bloodType'] as String?,
      qualification: json['qualification'] as String?,
      currentAddress: json['currentAddress'] as String?,
      governorate: json['governorate'] as String?,
      directorate: json['directorate'] as String?,
      isolation: json['isolation'] as String?,
      village: json['village'] as String?,
      jobTitle: json['jobTitle'] as String?,
      employmentStatus: json['employmentStatus'] as String?,
      salaryGrade: json['salaryGrade'] as String?,
      skills: json['skills'] as String?,
      hobbies: json['hobbies'] as String?,
      employeeNotes: json['employeeNotes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullNameArabic': fullNameArabic,
      'birthDate': birthDate?.toIso8601String(),
      'birthPlace': birthPlace,
      'bloodType': bloodType,
      'qualification': qualification,
      'currentAddress': currentAddress,
      'governorate': governorate,
      'directorate': directorate,
      'isolation': isolation,
      'village': village,
      'jobTitle': jobTitle,
      'employmentStatus': employmentStatus,
      'salaryGrade': salaryGrade,
      'skills': skills,
      'hobbies': hobbies,
      'employeeNotes': employeeNotes,
    };
  }
}

/// DTO للبحث في الموظفين
class EmployeeSearchDto {
  final String? searchTerm;
  final int? departmentId;
  final String? employmentStatus;
  final String? jobTitle;
  final DateTime? hireDateFrom;
  final DateTime? hireDateTo;
  final String? bloodType;
  final String? governorate;
  final int page;
  final int pageSize;
  final String sortBy;
  final String sortDirection;

  const EmployeeSearchDto({
    this.searchTerm,
    this.departmentId,
    this.employmentStatus,
    this.jobTitle,
    this.hireDateFrom,
    this.hireDateTo,
    this.bloodType,
    this.governorate,
    this.page = 1,
    this.pageSize = 10,
    this.sortBy = 'FullNameArabic',
    this.sortDirection = 'asc',
  });

  factory EmployeeSearchDto.fromJson(Map<String, dynamic> json) {
    return EmployeeSearchDto(
      searchTerm: json['searchTerm'] as String?,
      departmentId: json['departmentId'] as int?,
      employmentStatus: json['employmentStatus'] as String?,
      jobTitle: json['jobTitle'] as String?,
      hireDateFrom: json['hireDateFrom'] != null ? DateTime.parse(json['hireDateFrom'] as String) : null,
      hireDateTo: json['hireDateTo'] != null ? DateTime.parse(json['hireDateTo'] as String) : null,
      bloodType: json['bloodType'] as String?,
      governorate: json['governorate'] as String?,
      sortBy: json['sortBy'] as String? ?? 'FullNameArabic',
      sortDirection: json['sortDirection'] as String? ?? 'asc',
      page: json['page'] as int? ?? 1,
      pageSize: json['pageSize'] as int? ?? 10,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'searchTerm': searchTerm,
      'departmentId': departmentId,
      'employmentStatus': employmentStatus,
      'sortBy': sortBy,
      'sortDirection': sortDirection,
      'page': page,
      'pageSize': pageSize,
    };
  }
}

/// DTO لإحصائيات الموظفين
class EmployeeStatisticsDto {
  final int totalEmployees;
  final int activeEmployees;
  final int inactiveEmployees;
  final int terminatedEmployees;
  final int newHiresThisMonth;
  final int newHiresThisYear;
  final Map<String, int> employeesByDepartment;
  final Map<String, int> employeesByJobTitle;
  final Map<String, int> employeesByBloodType;
  final Map<String, int> employeesByGovernorate;

  const EmployeeStatisticsDto({
    required this.totalEmployees,
    required this.activeEmployees,
    required this.inactiveEmployees,
    required this.terminatedEmployees,
    required this.newHiresThisMonth,
    required this.newHiresThisYear,
    required this.employeesByDepartment,
    required this.employeesByJobTitle,
    required this.employeesByBloodType,
    required this.employeesByGovernorate,
  });

  factory EmployeeStatisticsDto.fromJson(Map<String, dynamic> json) {
    return EmployeeStatisticsDto(
      totalEmployees: json['totalEmployees'] as int,
      activeEmployees: json['activeEmployees'] as int,
      inactiveEmployees: json['inactiveEmployees'] as int,
      terminatedEmployees: json['terminatedEmployees'] as int,
      newHiresThisMonth: json['newHiresThisMonth'] as int? ?? 0,
      newHiresThisYear: json['newHiresThisYear'] as int? ?? 0,
      employeesByDepartment: Map<String, int>.from(json['employeesByDepartment'] ?? {}),
      employeesByJobTitle: Map<String, int>.from(json['employeesByJobTitle'] ?? {}),
      employeesByBloodType: Map<String, int>.from(json['employeesByBloodType'] ?? {}),
      employeesByGovernorate: Map<String, int>.from(json['employeesByGovernorate'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalEmployees': totalEmployees,
      'activeEmployees': activeEmployees,
      'inactiveEmployees': inactiveEmployees,
      'terminatedEmployees': terminatedEmployees,
      'newHiresThisMonth': newHiresThisMonth,
      'newHiresThisYear': newHiresThisYear,
      'employeesByDepartment': employeesByDepartment,
      'employeesByJobTitle': employeesByJobTitle,
      'employeesByBloodType': employeesByBloodType,
      'employeesByGovernorate': employeesByGovernorate,
    };
  }
}

/// DTO لتغيير حالة التوظيف
class ChangeEmploymentStatusDto {
  final String employmentStatus;
  final String? reason;
  final DateTime? effectiveDate;
  final String? notes;

  const ChangeEmploymentStatusDto({
    required this.employmentStatus,
    this.reason,
    this.effectiveDate,
    this.notes,
  });

  factory ChangeEmploymentStatusDto.fromJson(Map<String, dynamic> json) {
    return ChangeEmploymentStatusDto(
      employmentStatus: json['employmentStatus'] as String,
      reason: json['reason'] as String?,
      effectiveDate: DateTime.parse(json['effectiveDate'] as String),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employmentStatus': employmentStatus,
      'reason': reason,
      'effectiveDate': effectiveDate?.toIso8601String(),
      'notes': notes,
    };
  }
}
