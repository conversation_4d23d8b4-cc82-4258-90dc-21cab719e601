import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'hr_integration.dart';
import 'hr_module_info.dart';
import 'routes/hr_routes.dart';

/// تطبيق تجريبي لوحدة الموارد البشرية
class HRDemoApp extends StatelessWidget {
  const HRDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'وحدة الموارد البشرية - تجريبي',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
        ),
      ),
      locale: const Locale('ar', 'SA'),
      fallbackLocale: const Locale('en', 'US'),
      initialRoute: '/',
      getPages: [
        GetPage(
          name: '/',
          page: () => const HRDemoHomeScreen(),
        ),
        // ...HRPages.pages,
      ],
    );
  }
}

/// الشاشة الرئيسية للتطبيق التجريبي
class HRDemoHomeScreen extends StatelessWidget {
  const HRDemoHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وحدة الموارد البشرية'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الوحدة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue),
                        const SizedBox(width: 8),
                        Text(
                          'معلومات الوحدة',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ],
                    ),
                    const Divider(),
                    Text('الاسم: ${HRModuleInfo.nameArabic}'),
                    Text('الإصدار: ${HRModuleInfo.version}'),
                    Text('الوصف: ${HRModuleInfo.description}'),
                    const SizedBox(height: 8),
                    Text(
                      'الميزات المكتملة:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ...HRModuleInfo.completedFeatures.map(
                      (feature) => Text('• $feature'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // الشاشات المتاحة
            Text(
              'الشاشات المتاحة:',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1.2,
                children: [
                  _buildScreenCard(
                    context,
                    'لوحة التحكم',
                    Icons.dashboard,
                    Colors.blue,
                    () => Get.toNamed(HRRoutes.hr),
                  ),
                  _buildScreenCard(
                    context,
                    'الموظفين',
                    Icons.people,
                    Colors.green,
                    () => Get.toNamed(HRRoutes.employees),
                  ),
                  _buildScreenCard(
                    context,
                    'إضافة موظف',
                    Icons.person_add,
                    Colors.orange,
                    () => Get.toNamed(HRRoutes.addEmployee),
                  ),
                  _buildScreenCard(
                    context,
                    'الحضور',
                    Icons.access_time,
                    Colors.purple,
                    () => Get.toNamed(HRRoutes.attendance),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // معلومات إضافية
            Card(
              color: Colors.amber[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.amber[700]),
                        const SizedBox(width: 8),
                        Text(
                          'ملاحظة مهمة',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.amber[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذا تطبيق تجريبي لوحدة الموارد البشرية. '
                      'للاستخدام الفعلي، يجب ربط الوحدة بـ Backend API حقيقي.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScreenCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// تطبيق تجريبي مع بيانات وهمية
class HRDemoWithMockData extends StatefulWidget {
  const HRDemoWithMockData({super.key});

  @override
  State<HRDemoWithMockData> createState() => _HRDemoWithMockDataState();
}

class _HRDemoWithMockDataState extends State<HRDemoWithMockData> {
  @override
  void initState() {
    super.initState();
    _initializeDemo();
  }

  Future<void> _initializeDemo() async {
    try {
      // تهيئة الوحدة
      await HRIntegration.initialize();
      
      // تحميل بيانات وهمية
      _loadMockData();
      
      print('✅ تم تهيئة التطبيق التجريبي بنجاح');
    } catch (e) {
      print('❌ خطأ في تهيئة التطبيق التجريبي: $e');
    }
  }

  void _loadMockData() {
    // TODO: تحميل بيانات وهمية للاختبار
    print('📊 تم تحميل البيانات الوهمية');
  }

  @override
  Widget build(BuildContext context) {
    return const HRDemoApp();
  }
}

/// نقطة دخول التطبيق التجريبي
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تشغيل التطبيق التجريبي
  runApp(const HRDemoWithMockData());
}

/// مساعد لإنشاء بيانات وهمية للاختبار
class HRMockDataHelper {
  /// إنشاء موظفين وهميين
  static List<Map<String, dynamic>> createMockEmployees() {
    return [
      {
        'id': 1,
        'name': 'Ahmed Ali',
        'email': '<EMAIL>',
        'fullNameArabic': 'أحمد علي محمد',
        'employeeId': 'EMP001',
        'jobTitle': 'مطور برمجيات',
        'departmentName': 'تقنية المعلومات',
        'employmentStatus': 'active',
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      },
      {
        'id': 2,
        'name': 'Sara Hassan',
        'email': '<EMAIL>',
        'fullNameArabic': 'سارة حسن أحمد',
        'employeeId': 'EMP002',
        'jobTitle': 'محاسبة',
        'departmentName': 'المالية',
        'employmentStatus': 'active',
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      },
      {
        'id': 3,
        'name': 'Omar Khalil',
        'email': '<EMAIL>',
        'fullNameArabic': 'عمر خليل محمود',
        'employeeId': 'EMP003',
        'jobTitle': 'مدير مشروع',
        'departmentName': 'إدارة المشاريع',
        'employmentStatus': 'active',
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      },
    ];
  }

  /// إنشاء سجلات حضور وهمية
  static List<Map<String, dynamic>> createMockAttendance() {
    final today = DateTime.now();
    return [
      {
        'id': 1,
        'employeeId': 1,
        'employeeName': 'Ahmed Ali',
        'employeeNameArabic': 'أحمد علي محمد',
        'attendanceDate': today.toIso8601String(),
        'checkInTime': today.copyWith(hour: 8, minute: 30).toIso8601String(),
        'checkOutTime': today.copyWith(hour: 17, minute: 0).toIso8601String(),
        'status': 'present',
        'totalHours': 8.5,
        'isLate': false,
        'isEarlyLeave': false,
        'createdAt': today.toIso8601String(),
      },
      {
        'id': 2,
        'employeeId': 2,
        'employeeName': 'Sara Hassan',
        'employeeNameArabic': 'سارة حسن أحمد',
        'attendanceDate': today.toIso8601String(),
        'checkInTime': today.copyWith(hour: 9, minute: 15).toIso8601String(),
        'status': 'late',
        'totalHours': null,
        'isLate': true,
        'isEarlyLeave': false,
        'createdAt': today.toIso8601String(),
      },
      {
        'id': 3,
        'employeeId': 3,
        'employeeName': 'Omar Khalil',
        'employeeNameArabic': 'عمر خليل محمود',
        'attendanceDate': today.toIso8601String(),
        'status': 'absent',
        'totalHours': null,
        'isLate': false,
        'isEarlyLeave': false,
        'createdAt': today.toIso8601String(),
      },
    ];
  }

  /// إنشاء إحصائيات وهمية
  static Map<String, dynamic> createMockStatistics() {
    return {
      'totalEmployees': 150,
      'activeEmployees': 142,
      'inactiveEmployees': 5,
      'terminatedEmployees': 3,
    };
  }
}
