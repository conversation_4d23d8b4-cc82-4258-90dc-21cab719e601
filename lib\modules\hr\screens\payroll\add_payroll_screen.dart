import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../widgets/employee_picker_widget.dart';
import '../../../../utils/user_helper.dart';

/// شاشة إضافة كشف راتب مكتملة
class AddPayrollScreen extends StatefulWidget {
  const AddPayrollScreen({super.key});

  @override
  State<AddPayrollScreen> createState() => _AddPayrollScreenState();
}

class _AddPayrollScreenState extends State<AddPayrollScreen> {
  final _formKey = GlobalKey<FormState>();
  late PayrollController _payrollController;

  // Controllers للحقول
  final _basicSalaryController = TextEditingController(text: '0');
  final _allowancesController = TextEditingController(text: '0');
  final _bonusesController = TextEditingController(text: '0');
  final _incentivesController = TextEditingController(text: '0');
  final _deductionsController = TextEditingController(text: '0');
  final _advanceDeductionsController = TextEditingController(text: '0');
  final _financialAssistanceController = TextEditingController(text: '0');
  final _notesController = TextEditingController();

  // متغيرات الحالة
  Employee? _selectedEmployee;
  DateTime? _payPeriodStart;
  DateTime? _payPeriodEnd;
  double _netSalary = 0.0;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _calculateNetSalary();
  }

  void _initializeControllers() {
    _payrollController = Get.find<PayrollController>();

    // إضافة مستمعين لحساب الراتب الصافي
    _basicSalaryController.addListener(_calculateNetSalary);
    _allowancesController.addListener(_calculateNetSalary);
    _bonusesController.addListener(_calculateNetSalary);
    _incentivesController.addListener(_calculateNetSalary);
    _deductionsController.addListener(_calculateNetSalary);
    _advanceDeductionsController.addListener(_calculateNetSalary);
    _financialAssistanceController.addListener(_calculateNetSalary);
  }

  void _calculateNetSalary() {
    final basicSalary = double.tryParse(_basicSalaryController.text) ?? 0.0;
    final allowances = double.tryParse(_allowancesController.text) ?? 0.0;
    final bonuses = double.tryParse(_bonusesController.text) ?? 0.0;
    final incentives = double.tryParse(_incentivesController.text) ?? 0.0;
    final financialAssistance = double.tryParse(_financialAssistanceController.text) ?? 0.0;
    final deductions = double.tryParse(_deductionsController.text) ?? 0.0;
    final advanceDeductions = double.tryParse(_advanceDeductionsController.text) ?? 0.0;

    final totalEarnings = basicSalary + allowances + bonuses + incentives + financialAssistance;
    final totalDeductions = deductions + advanceDeductions;
    
    setState(() {
      _netSalary = totalEarnings - totalDeductions;
    });
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _payPeriodStart ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _payPeriodStart) {
      setState(() {
        _payPeriodStart = picked;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _payPeriodEnd ?? DateTime.now(),
      firstDate: _payPeriodStart ?? DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _payPeriodEnd) {
      setState(() {
        _payPeriodEnd = picked;
      });
    }
  }

  Future<void> _savePayroll() async {
    if (!_formKey.currentState!.validate()) {
      Get.snackbar(
        'خطأ في البيانات',
        'يرجى التحقق من البيانات المدخلة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (_selectedEmployee == null) {
      Get.snackbar('خطأ', 'يرجى اختيار الموظف');
      return;
    }

    if (_payPeriodStart == null || _payPeriodEnd == null) {
      Get.snackbar('خطأ', 'يرجى تحديد فترة الراتب');
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final currentUserId = UserHelper.getCurrentUserId();

      final payrollData = CreatePayrollDto(
        employeeId: _selectedEmployee!.id,
        payPeriodStart: _payPeriodStart!,
        payPeriodEnd: _payPeriodEnd!,
        basicSalary: double.parse(_basicSalaryController.text),
        allowances: double.parse(_allowancesController.text),
        bonuses: double.parse(_bonusesController.text),
        incentives: double.parse(_incentivesController.text),
        deductions: double.parse(_deductionsController.text),
        advanceDeductions: double.parse(_advanceDeductionsController.text),
        financialAssistance: double.parse(_financialAssistanceController.text),
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        createdBy: currentUserId,
      );

      final success = await _payrollController.createPayroll(payrollData);
      
      if (success) {
        Get.snackbar(
          'تم الحفظ',
          'تم إنشاء كشف الراتب بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Get.back(result: true);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ في الحفظ',
        'حدث خطأ أثناء حفظ البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة كشف راتب'),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: _isSaving 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            onPressed: _isSaving ? null : _savePayroll,
            tooltip: _isSaving ? 'جاري الحفظ...' : 'حفظ كشف الراتب',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEmployeeSection(),
              const SizedBox(height: 16),
              _buildPeriodSection(),
              const SizedBox(height: 16),
              _buildSalarySection(),
              const SizedBox(height: 16),
              _buildNetSalaryCard(),
              const SizedBox(height: 16),
              _buildNotesSection(),
              const SizedBox(height: 24),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الموظف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            EmployeePickerWidget(
              selectedEmployee: _selectedEmployee,
              onEmployeeSelected: (employee) {
                setState(() {
                  _selectedEmployee = employee;
                });
              },
              onBasicSalaryChanged: (basicSalary) {
                setState(() {
                  _basicSalaryController.text = basicSalary.toString();
                  _calculateNetSalary();
                });
              },
              labelText: 'اختيار الموظف',
              hintText: 'اضغط لاختيار الموظف',
              isRequired: true,
              validator: (employee) {
                if (employee == null) {
                  return 'يرجى اختيار الموظف';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فترة الراتب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectStartDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ البداية',
                        prefixIcon: Icon(Icons.calendar_today),
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        _payPeriodStart != null
                            ? '${_payPeriodStart!.year}/${_payPeriodStart!.month.toString().padLeft(2, '0')}/${_payPeriodStart!.day.toString().padLeft(2, '0')}'
                            : 'اختر تاريخ البداية',
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ النهاية',
                        prefixIcon: Icon(Icons.calendar_today),
                        border: OutlineInputBorder(),
                      ),
                      child: Text(
                        _payPeriodEnd != null
                            ? '${_payPeriodEnd!.year}/${_payPeriodEnd!.month.toString().padLeft(2, '0')}/${_payPeriodEnd!.day.toString().padLeft(2, '0')}'
                            : 'اختر تاريخ النهاية',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مكونات الراتب',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // المكاسب
            const Text(
              'المكاسب',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.green),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _basicSalaryController,
                    decoration: const InputDecoration(
                      labelText: 'الراتب الأساسي',
                      prefixIcon: Icon(Icons.attach_money),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _allowancesController,
                    decoration: const InputDecoration(
                      labelText: 'البدلات',
                      prefixIcon: Icon(Icons.add_circle),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _bonusesController,
                    decoration: const InputDecoration(
                      labelText: 'المكافآت',
                      prefixIcon: Icon(Icons.star),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _incentivesController,
                    decoration: const InputDecoration(
                      labelText: 'الحوافز',
                      prefixIcon: Icon(Icons.trending_up),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _financialAssistanceController,
              decoration: const InputDecoration(
                labelText: 'المساعدات المالية',
                prefixIcon: Icon(Icons.help),
                border: OutlineInputBorder(),
                suffixText: 'ريال',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            ),

            const SizedBox(height: 16),

            // الخصومات
            const Text(
              'الخصومات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.red),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _deductionsController,
                    decoration: const InputDecoration(
                      labelText: 'الخصومات العامة',
                      prefixIcon: Icon(Icons.remove_circle),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _advanceDeductionsController,
                    decoration: const InputDecoration(
                      labelText: 'خصومات السلف',
                      prefixIcon: Icon(Icons.money_off),
                      border: OutlineInputBorder(),
                      suffixText: 'ريال',
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetSalaryCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'الراتب الصافي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '${_netSalary.toStringAsFixed(2)} ريال',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: _netSalary >= 0 ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
                hintText: 'أي ملاحظات حول كشف الراتب',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _savePayroll,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple.shade700,
          foregroundColor: Colors.white,
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('جاري الحفظ...'),
                ],
              )
            : const Text(
                'حفظ كشف الراتب',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  @override
  void dispose() {
    _basicSalaryController.dispose();
    _allowancesController.dispose();
    _bonusesController.dispose();
    _incentivesController.dispose();
    _deductionsController.dispose();
    _advanceDeductionsController.dispose();
    _financialAssistanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
