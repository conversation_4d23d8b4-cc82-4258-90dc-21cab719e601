import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hr_controllers.dart';
import '../../models/hr_models.dart';
import '../../routes/hr_routes.dart';

/// شاشة تفاصيل كشف الراتب
class PayrollDetailsScreen extends StatelessWidget {
  const PayrollDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final payrollId = int.tryParse(Get.parameters['id'] ?? '0') ?? 0;
    
    // التأكد من تسجيل Controller
    if (!Get.isRegistered<PayrollController>()) {
      Get.lazyPut(() => PayrollController());
    }
    
    final payrollController = Get.find<PayrollController>();
    
    // تحميل تفاصيل الراتب
    final payrollObs = Rx<EmployeePayroll?>(null);
    final isLoading = true.obs;
    
    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final payroll = await payrollController.getPayrollById(payrollId);
        payrollObs.value = payroll;
      } catch (e) {
        Get.snackbar('خطأ', 'فشل في تحميل تفاصيل الراتب');
      } finally {
        isLoading.value = false;
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل كشف الراتب'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Obx(() {
            final payroll = payrollObs.value;
            if (payroll == null) return const SizedBox.shrink();
            
            return PopupMenuButton<String>(
              onSelected: (value) => _handleAction(value, payroll),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                  ),
                ),
                if (payroll.paymentStatus == 'pending')
                  const PopupMenuItem(
                    value: 'approve',
                    child: ListTile(
                      leading: Icon(Icons.check),
                      title: Text('اعتماد الدفع'),
                    ),
                  ),
                const PopupMenuItem(
                  value: 'print',
                  child: ListTile(
                    leading: Icon(Icons.print),
                    title: Text('طباعة'),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
      body: Obx(() {
        if (isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final payroll = payrollObs.value;
        if (payroll == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text(
                  'لم يتم العثور على كشف الراتب',
                  style: TextStyle(fontSize: 18),
                ),
              ],
            ),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الموظف
              _buildEmployeeInfoCard(payroll),
              const SizedBox(height: 16),
              
              // معلومات الفترة
              _buildPeriodInfoCard(payroll),
              const SizedBox(height: 16),
              
              // تفاصيل الراتب
              _buildSalaryDetailsCard(payroll),
              const SizedBox(height: 16),
              
              // معلومات الدفع
              _buildPaymentInfoCard(payroll),
              const SizedBox(height: 16),
              
              // الملاحظات
              if (payroll.notes != null && payroll.notes!.isNotEmpty)
                _buildNotesCard(payroll),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildEmployeeInfoCard(EmployeePayroll payroll) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الموظف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    payroll.employeeNameArabic ?? payroll.employeeName ?? 'غير محدد',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.badge, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'معرف الموظف: ${payroll.employeeId}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodInfoCard(EmployeePayroll payroll) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فترة الراتب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.date_range, color: Colors.orange),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    payroll.payPeriodFormatted,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_month, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  payroll.monthYear,
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryDetailsCard(EmployeePayroll payroll) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الراتب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // الراتب الأساسي
            _buildSalaryRow('الراتب الأساسي', payroll.basicSalary, Colors.blue),
            
            // البدلات
            if (payroll.allowances > 0)
              _buildSalaryRow('البدلات', payroll.allowances, Colors.green),
            
            // المكافآت
            if (payroll.bonuses > 0)
              _buildSalaryRow('المكافآت', payroll.bonuses, Colors.orange),
            
            // الحوافز
            if (payroll.incentives > 0)
              _buildSalaryRow('الحوافز', payroll.incentives, Colors.purple),
            
            // المساعدات المالية
            if (payroll.financialAssistance > 0)
              _buildSalaryRow('المساعدات المالية', payroll.financialAssistance, Colors.teal),
            
            const Divider(),
            
            // إجمالي المكاسب
            _buildSalaryRow('إجمالي المكاسب', payroll.totalEarnings, Colors.green, isBold: true),
            
            const SizedBox(height: 8),
            
            // الخصومات
            if (payroll.deductions > 0)
              _buildSalaryRow('الخصومات', payroll.deductions, Colors.red),
            
            // خصم السلف
            if (payroll.advanceDeductions > 0)
              _buildSalaryRow('خصم السلف', payroll.advanceDeductions, Colors.red),
            
            // إجمالي الخصومات
            if (payroll.totalDeductions > 0) ...[
              const Divider(),
              _buildSalaryRow('إجمالي الخصومات', payroll.totalDeductions, Colors.red, isBold: true),
            ],
            
            const Divider(thickness: 2),
            
            // الراتب الصافي
            _buildSalaryRow('الراتب الصافي', payroll.netSalary, Colors.blue, isBold: true, isLarge: true),
          ],
        ),
      ),
    );
  }

  Widget _buildSalaryRow(String label, double amount, Color color, {bool isBold = false, bool isLarge = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isLarge ? 16 : 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '${amount.toStringAsFixed(2)} ريال',
            style: TextStyle(
              fontSize: isLarge ? 16 : 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard(EmployeePayroll payroll) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الدفع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  payroll.isPaid ? Icons.check_circle : Icons.pending,
                  color: payroll.isPaid ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'الحالة: ${payroll.paymentStatusArabic}',
                  style: TextStyle(
                    fontSize: 16,
                    color: payroll.isPaid ? Colors.green : Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (payroll.paymentDate != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.payment, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    'تاريخ الدفع: ${payroll.paymentDate!.day}/${payroll.paymentDate!.month}/${payroll.paymentDate!.year}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(EmployeePayroll payroll) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملاحظات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              payroll.notes!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  void _handleAction(String action, EmployeePayroll payroll) {
    switch (action) {
      case 'edit':
        // الانتقال لشاشة التعديل
        HRNavigator.toEditPayroll(payroll.id);
        break;
      case 'approve':
        _approvePayment(payroll);
        break;
      case 'print':
        _printPayroll(payroll);
        break;
    }
  }

  void _approvePayment(EmployeePayroll payroll) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الدفع'),
        content: Text('هل تريد تأكيد دفع راتب ${payroll.employeeNameArabic}؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              final controller = Get.find<PayrollController>();
              controller.approvePayment(payroll.id);
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _printPayroll(EmployeePayroll payroll) {
    Get.snackbar(
      'طباعة',
      'سيتم إضافة وظيفة الطباعة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
